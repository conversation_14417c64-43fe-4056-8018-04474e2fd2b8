import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {
    BehaviorSubject,
    combineLatest,
    from,
    Observable,
    ReplaySubject,
    timer,
    zip
} from 'rxjs';
import {
    distinctUntilChanged,
    map,
    shareReplay,
    startWith,
    switchMap,
    tap
} from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { debug } from '../../core/operators';
import {
    Aggregation,
    AssetCategoryAPICode,
    calculateAttributeValue,
    CosmosAsset,
    CosmosProject,
    CosmosTask,
    CosmosUser,
    CosmosWorkOrder
} from '../models';
import { ApmService } from './apm.service';

const REFRESH_INTERVAL_MS = 5 * 60 * 1000;

@Injectable()
export class DashboardService {
    private readonly _selectedProjectIds = new BehaviorSubject<string[]>([]);
    private readonly _loadingProjects = new BehaviorSubject<boolean>(false);
    private readonly _loadingTasks = new BehaviorSubject<boolean>(false);
    private readonly _loadingAssets = new BehaviorSubject<boolean>(false);
    private readonly _loadingWorkOrders = new BehaviorSubject<boolean>(false);

    private readonly _manualRefresh = new ReplaySubject<void>(1);
    private readonly _refresh = this._manualRefresh.pipe(
        startWith(undefined),
        switchMap(() => timer(0, REFRESH_INTERVAL_MS)),
        shareReplay(1)
    );

    /**
     * All work orders for the user considering all of their effective business units
     */
    private readonly _workOrders$ = combineLatest([
        this._apm.apmUser$,
        this._refresh
    ]).pipe(
        switchMap(([apmUser]) => {
            this._loadingWorkOrders.next(true);
            return this.getResourcesForUser<CosmosWorkOrder>(
                apmUser,
                'workorders'
            );
        }),
        tap(() => this._loadingWorkOrders.next(false)),
        debug('GET Work orders'),
        shareReplay()
    );

    /**
     * All assets for the user considering all of their effective business units
     */
    private readonly _assets$ = combineLatest([
        this._apm.apmUser$,
        this._refresh
    ]).pipe(
        switchMap(([apmUser]) => {
            this._loadingAssets.next(true);
            return this.getResourcesForUser<CosmosAsset>(apmUser, 'assets');
        }),
        tap(() => this._loadingAssets.next(false)),
        debug('GET Assets'),
        shareReplay(1)
    );

    /**
     * All projects for the user considering all of their effective business units
     */
    private readonly _projects$ = combineLatest([
        this._apm.apmUser$,
        this._refresh
    ]).pipe(
        switchMap(([apmUser]) => {
            this._loadingProjects.next(true);
            return this.getResourcesForUser<CosmosProject>(
                apmUser,
                'projects'
            );
        }),
        map((projects) =>
            projects.map((project) => {
                for (let key in project) {
                    if (
                        project[key] !== undefined &&
                        project[key].Value === undefined &&
                        project[key].ValueChangeLog !== undefined
                    ) {
                        project[key].Value = calculateAttributeValue(
                            project[key]
                        );
                    }
                }
                return project;
            })
        ),
        tap(() => this._loadingProjects.next(false)),
        debug('GET Projects'),
        shareReplay()
    );

    /**
     * All tasks for the user considering all of their effective business units
     */
    private readonly _tasks$ = combineLatest([
        this._apm.apmUser$,
        this._refresh
    ]).pipe(
        switchMap(([apmUser]) => {
            this._loadingTasks.next(true);
            return this.getResourcesForUser<CosmosTask>(apmUser, 'tasks');
        }),
        map((tasks) => {
            return tasks.map((task) => {
                for (let key in task) {
                    if (
                        task[key] !== undefined &&
                        task[key].Value === undefined &&
                        task[key].ValueChangeLog !== undefined
                    ) {
                        task[key].Value = calculateAttributeValue(task[key]);
                    }
                }
                return task;
            });
        }),
        tap(() => this._loadingTasks.next(false)),
        debug('GET Tasks'),
        shareReplay(1)
    );

    inspectionStatusCategories = new BehaviorSubject<AssetCategoryAPICode[]>([
        '510'
    ]);

    /**
     * Loading state for the assets donut chart
     */
    loadingAssetsDonutChart$ = this._loadingAssets.asObservable();

    /**
     * Loading state for the assets by area and type chart
     */
    loadingAssetsByAreaAndType$ = this._loadingAssets.asObservable();

    /**
     * Loading state for the inspection statuses chart
     */
    loadingInspectionStatuses$ = this._loadingTasks.asObservable();

    /**
     * Loading state for the summary info chart
     */
    loadingSummaryInfo$ = combineLatest([
        this._loadingProjects,
        this._loadingTasks
    ]).pipe(
        map(
            ([loadingProjects, loadingTasks]) => loadingProjects || loadingTasks
        )
    );

    /**
     * Loading state for the statuses by month chart
     */
    loadingStatusesByMonth$ = combineLatest([
        this._loadingTasks,
        this._loadingAssets
    ]).pipe(
        map(([loadingTasks, loadingAssets]) => loadingTasks || loadingAssets)
    );

    /**
     * Loading state for the statuses by week chart
     */
    loadingStatusesByWeek$ = combineLatest([
        this._loadingTasks,
        this._loadingAssets
    ]).pipe(
        map(([loadingTasks, loadingAssets]) => loadingTasks || loadingAssets)
    );

    /**
     * Loading state for the inspections without due dates chart
     */
    loadingNoDueDatesInspections$ = combineLatest([
        this._loadingTasks,
        this._loadingAssets
    ]).pipe(
        map(([loadingTasks, loadingAssets]) => {
            return loadingTasks || loadingAssets;
        })
    );

    /**
     * Loading state for the activity summary chart
     */
    loadingActivitySummary$ = combineLatest([
        this._loadingProjects,
        this._loadingTasks
    ]).pipe(
        map(([loadingProjects, loadingTasks]) => {
            return loadingProjects || loadingTasks;
        })
    );

    /**
     * Loading state for the timeline
     */
    loadingTimeline$ = combineLatest([
        this._loadingWorkOrders,
        this._loadingProjects,
        this._loadingAssets,
        this._loadingTasks
    ]).pipe(
        map(
            ([
                loadingProjects,
                loadingAssets,
                loadingTasks,
                loadingWorkOrders
            ]) => {
                return (
                    loadingProjects ||
                    loadingAssets ||
                    loadingTasks ||
                    loadingWorkOrders
                );
            }
        )
    );

    /**
     * Projects filtered to the selected Business Unit
     */
    selectedBUProjects$ = combineLatest([
        this._apm.selectedBU$,
        this._projects$
    ]).pipe(
        map(([selectedBU, projects]) =>
            projects.filter(
                (project) => project.BusinessUnitId.Value === selectedBU
            )
        ),
        shareReplay(1)
    );

    /**
     * Tasks filtered to the selected Business Unit
     */
    selectedBUTasks$ = combineLatest([
        this._apm.selectedBU$,
        this._tasks$
    ]).pipe(
        map(([selectedBU, tasks]) => {
            return tasks.filter(
                (task) => task.BusinessUnitId.Value === selectedBU
            );
        }),
        shareReplay(1)
    );

    /** Assets filtered to the selected Business Unit */
    selectedBUAssets$ = combineLatest([
        this._apm.selectedBU$,
        this._assets$
    ]).pipe(
        map(([selectedBU, assets]) => {
            return assets.filter(
                (project) => project.BusinessUnitId.Value === selectedBU
            );
        }),
        shareReplay(1)
    );

    /** Work orders filtered to the selected Business Unit */
    selectedBUWorkOrders$ = combineLatest([
        this._apm.selectedBU$,
        this._workOrders$
    ]).pipe(
        map(([selectedBU, workOrders]) =>
            workOrders.filter(
                (workOrder) => workOrder.BusinessUnitId.Value === selectedBU
            )
        ),
        shareReplay(1)
    );

    /**
     * The selected project ids
     */
    selectedProjectIds$ = this._selectedProjectIds.asObservable().pipe(
        distinctUntilChanged(
            (previous, current) =>
                (previous.length === 0 && current.length === 0) ||
                (previous.every((id) => current.includes(id)) &&
                    current.every((id) => previous.includes(id)))
        ),
        shareReplay(1)
    );

    /**
     * Data for asset categories chart
     */
    assetsCategoriesSummary$ = combineLatest([
        this.selectedProjectIds$,
        this.selectedBUAssets$
    ]).pipe(
        map(([projectIds, assets]) =>
            Aggregation.getAssetCategoriesSummary(assets, projectIds)
        )
    );

    /**
     * Data for the summary info chart
     */
    summaryInfo$ = combineLatest([
        this.selectedProjectIds$,
        zip(
            this.selectedBUProjects$,
            this.selectedBUTasks$,
            this.selectedBUWorkOrders$
        )
    ]).pipe(
        map(([projectIds, [projects, tasks, workOrders]]) =>
            Aggregation.getSummaryInfo(projects, projectIds, tasks, workOrders)
        )
    );

    /**
     * Data for the inspection statuses chart
     */
    inspectionStatuses$ = combineLatest([
        this.selectedProjectIds$,
        this.selectedBUTasks$,
        this.selectedBUAssets$,
        this.inspectionStatusCategories.asObservable()
    ]).pipe(
        map(([projectIds, tasks, assets, categories]) =>
            Aggregation.getInspectionStatuses(
                projectIds,
                tasks,
                assets,
                categories
            )
        )
    );

    /**
     * Data for the timeline widget
     */
    private _timelineData$ = combineLatest([
        this.selectedProjectIds$,
        zip(
            this.selectedBUProjects$,
            this.selectedBUTasks$,
            this.selectedBUAssets$,
            this.selectedBUWorkOrders$
        )
    ]).pipe(
        map(([projectIds, [projects, tasks, assets, workOrders]]) =>
            Aggregation.getTimelineTasksAndProjects(
                projectIds,
                tasks,
                projects,
                workOrders
            )
        )
    );
    timelineTasks$ = this._timelineData$.pipe(map((data) => data[0]));
    timelineProjects$ = this._timelineData$.pipe(map((data) => data[1]));

    /**
     * Data for the statuses by month chart
     */
    statusesByMonth$ = combineLatest([
        this.selectedProjectIds$,
        zip(this.selectedBUTasks$, this.selectedBUAssets$)
    ]).pipe(
        map(([projectIds, [tasks, assets]]) =>
            Aggregation.getStatusesByMonth(projectIds, tasks, assets)
        )
    );

    /**
     * Data for the statuses by week chart
     */
    statusesByWeek$ = combineLatest([
        this.selectedProjectIds$,
        zip(this.selectedBUTasks$, this.selectedBUAssets$)
    ]).pipe(
        map(([projectIds, [tasks, assets]]) =>
            Aggregation.getStatusesByWeek(projectIds, tasks, assets)
        )
    );

    /**
     * Data for the assets by area and type chart
     */
    assetsByAreaAndType$ = combineLatest([
        this.selectedProjectIds$,
        this.selectedBUAssets$
    ]).pipe(
        map(([projectIds, assets]) =>
            Aggregation.getAssetsByAreaAndType(projectIds, assets)
        )
    );

    /**
     * Data for the inspections without due dates chart
     */
    inspectionsWithoutDueDates$ = combineLatest([
        this.selectedProjectIds$,
        zip(this.selectedBUTasks$, this.selectedBUAssets$)
    ]).pipe(
        map(([projectIds, [tasks, assets]]) => {
            return Aggregation.createInspectionsWithoutDueDates(
                projectIds,
                tasks,
                assets
            );
        })
    );

    /**
     * Data for the activity summary chart
     */
    activitySummary$ = combineLatest([
        this.selectedProjectIds$,
        zip(this.selectedBUProjects$, this.selectedBUTasks$)
    ]).pipe(
        map(([projectIds, [projects, tasks]]) =>
            Aggregation.getActivitySummary(projectIds, projects, tasks)
        )
    );

    constructor(
        private readonly _http: HttpClient,
        private readonly _apm: ApmService
    ) {}

    /**
     * Broadcasts the selected project ids
     * @param projectIds the project ids that have been selected
     */
    selectProjects(projectIds: string[]) {
        this._selectedProjectIds.next(projectIds);
    }

    /**
     * Refresh the data for the dashboard
     */
    refresh() {
        this._manualRefresh.next();
    }

    /**
     * Returns resources (documents) that the user has access to via their
     * effective business units.
     *
     * @param apmUser user that is signed in
     * @param path API endpoint path
     * @returns collection of documents
     */
    private getResourcesForUser<T>(
        apmUser: CosmosUser,
        path: string
    ): Observable<T[]> {
        // Migrated to use backend API instead of direct Cosmos DB queries
        const businessUnitIds = apmUser.EffectiveBusinessUnitIds?.join(',') || '';
        const apiUrl = `${environment.api.url}/${path}?businessUnitIds=${businessUnitIds}`;

        return this._http.get<T[]>(apiUrl);
    }
}
