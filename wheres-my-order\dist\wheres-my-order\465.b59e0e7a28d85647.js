"use strict";(self.webpackChunkwheres_my_order=self.webpackChunkwheres_my_order||[]).push([[465],{22465:(ke,I,r)=>{r.r(I),r.d(I,{AdminModule:()=>Qe});var _=r(36895),G=r(98069),P=r(67837),O=r(98350),D=r(61978),u=r(47259),w=r(60305),S=r(88744),L=r(83108),y=r(52599),J=r(5128),f=r(39646),v=r(54004),K=r(18505),m=r(63326);class h{}var e=r(98274),A=r(31140),F=r(15697),Q=r(99106),l=r(84943),k=r(72834);function V(s,a){if(1&s&&(e.TgZ(0,"span",25),e._uU(1),e.qZA()),2&s){const t=e.oxw();e.xp6(1),e.Oqu(t.roleEdit.id)}}function q(s,a){if(1&s&&(e.TgZ(0,"div")(1,"div"),e._uU(2),e.qZA()()),2&s){const t=a.$implicit;e.xp6(2),e.AsE("",t.name," (",t.id,") ")}}const B=function(){return["name","id"]};function Y(s,a){if(1&s){const t=e.EpF();e.ynx(0),e.TgZ(1,"div",26)(2,"dx-select-box",27,28),e.NdJ("selectedItemChange",function(o){e.CHM(t);const n=e.oxw();return e.KtG(n.selectedUser=o)}),e.ALo(4,"async"),e.qZA(),e.TgZ(5,"dx-button",29),e.NdJ("onClick",function(o){e.CHM(t);const n=e.oxw();return e.KtG(n.addUserClicked(o))}),e.qZA()(),e.TgZ(6,"dx-list",30,31),e.NdJ("onItemDeleting",function(o){e.CHM(t);const n=e.oxw();return e.KtG(n.removeUserClicked(o))}),e.ALo(8,"async"),e.ALo(9,"async"),e.YNc(10,q,3,2,"div",32),e.qZA(),e.BQk()}if(2&s){const t=e.oxw();e.xp6(2),e.Q6J("dataSource",e.lcZ(4,16,t.availableUsers))("selectedItem",t.selectedUser)("minSearchLength",2)("searchEnabled",!0)("searchExpr",e.DdM(22,B))("searchTimeout",200)("showDataBeforeSearch",!0)("showDropDownButton",!1)("disabled","app:admin"===t.roleOriginal.id.toLowerCase()&&!t.isUserAppAdmin),e.xp6(3),e.Q6J("disabled","app:admin"===t.roleOriginal.id.toLowerCase()&&!t.isUserAppAdmin),e.xp6(1),e.Q6J("items",e.lcZ(8,18,t.usersForSelectedRole$))("allowItemDeleting","app:admin"===t.roleOriginal.id.toLowerCase()&&t.isUserAppAdmin||"app:admin"!==t.roleOriginal.id.toLowerCase())("selectedItemKeys",e.lcZ(9,20,t.selectedUserInList))("activeStateEnabled",!0)("focusStateEnabled",!0),e.xp6(4),e.Q6J("dxTemplateOf","item")}}function H(s,a){if(1&s){const t=e.EpF();e.TgZ(0,"dx-button",44),e.NdJ("onClick",function(o){e.CHM(t);const n=e.oxw(2);return e.KtG(n.updateClicked(o))}),e.qZA()}}function j(s,a){if(1&s){const t=e.EpF();e.TgZ(0,"dx-button",45),e.NdJ("onClick",function(o){e.CHM(t);const n=e.oxw(2);return e.KtG(n.createClicked(o))}),e.qZA()}}const z=function(){return{text:"Role Key"}};function W(s,a){if(1&s){const t=e.EpF();e.ynx(0),e.TgZ(1,"dx-form",33,34),e.NdJ("formDataChange",function(o){e.CHM(t);const n=e.oxw();return e.KtG(n.roleEdit=o)}),e.TgZ(3,"dxi-item",35),e._UZ(4,"dxi-validation-rule",36)(5,"dxi-validation-rule",37),e.qZA(),e.TgZ(6,"dxi-item",38),e._UZ(7,"dxi-validation-rule",39),e.qZA()(),e.TgZ(8,"div",40),e.YNc(9,H,1,0,"dx-button",41),e.YNc(10,j,1,0,"dx-button",42),e.TgZ(11,"dx-button",43),e.NdJ("onClick",function(o){e.CHM(t);const n=e.oxw();return e.KtG(n.cancelClicked(o))}),e.qZA()(),e.BQk()}if(2&s){const t=e.oxw();e.xp6(1),e.Q6J("readOnly","none"===t.mode)("formData",t.roleEdit)("colCount",1)("showValidationSummary",!0),e.xp6(2),e.Q6J("label",e.DdM(7,z)),e.xp6(6),e.Q6J("ngIf","editRole"===t.mode),e.xp6(1),e.Q6J("ngIf","newRole"===t.mode)}}let X=(()=>{class s{constructor(t,i,o){this._users=t,this._roles=i,this._toastNotificationService=o,this.crumbs=[{label:"Administration",route:"/admin"},{label:"Roles",route:"/admin/roles"}],this.isFormValid=!1,this.loadingVisible=!0,this.mode="none",this.roleEdit=new h,this.roleOriginal=new h}get title(){switch(this.mode){case"editRole":return"Edit Role";case"newRole":return"New Role";default:return"Role Membership"}}ngOnInit(){this.loadSearchList(),this.availableUsers=this._users.getAll(),this._users.currentProfile$.subscribe(i=>{this.currentUser=i,this.isUserAppAdmin=this.currentUser.roles.some(o=>"app:admin"===o.toLowerCase())})}roleSelected(t){t.rowIndex>=0&&"data"===t.row.rowType&&(this.mode="roleSelected",this.roleOriginal=new h,Object.assign(this.roleOriginal,t.row.data),Object.assign(this.roleEdit,this.roleOriginal),this.usersForSelectedRole$=this._roles.getUsersForRole(this.roleOriginal.id))}addUserClicked(t){if(!this.selectedUser)return;const i=this.roleOriginal.id;this._roles.addUserToRole(i,this.selectedUser.id).subscribe(n=>this.usersForSelectedRole$=this._roles.getUsersForRole(i),n=>{403===n.status&&this._toastNotificationService.show("Forbidden","Accounts with common personal email domains cannot be given permissions. Please inform the user to create a new account with their work email and retry.",m.pC.error)}),this.selectedUserInList=this.usersForSelectedRole$.pipe((0,v.U)(n=>[this.selectedUser.id]),(0,K.b)(n=>this.userSelectBox.instance.reset()))}removeUserClicked(t){this._roles.deleteUserFromRole(this.roleOriginal.id,t.itemData.id).subscribe()}addMenuItems(t){"content"===t.target&&("data"===t.row.rowType&&(t.items||(t.items=[]),t.items.push({text:"Edit Role Properties...",icon:"fa fa-pencil-square-o",onItemClick:()=>{this.roleEditClicked(t.row)}})),"group"===t.row.rowType&&(t.items||(t.items=[]),t.items.push({text:"Add New Role...",icon:"fa fa-plus",onItemClick:()=>{this.newRoleClicked(t.row)}})))}roleEditClicked(t){this.mode="editRole"}roleDeleteClicked(t){(0,J.iG)(`Are you sure you want to delete Role (${this.roleEdit.id})?`,"Confirm Delete").then(o=>{o&&this.deleteRole()})}createClicked(t){this.validateAndExecute(t,()=>this.createRole())}updateClicked(t){this.validateAndExecute(t,()=>this.updateRole())}newRoleClicked(t){console.warn(t),this.mode="newRole",this.focusedRowKey=void 0,this.roleOriginal=new h,this.roleEdit=new h,t.data&&(this.roleEdit.id=t.data.key+":")}cancelClicked(t){"newRole"===this.mode?(this.roleEdit=new h,this.roleOriginal=new h,this.mode="none"):(Object.assign(this.roleEdit,this.roleOriginal),this.mode="roleSelected",this.usersForSelectedRole$=this._roles.getUsersForRole(this.roleOriginal.id)),this.selectedUser=void 0}refreshRolesClicked(t){this.setDefaultState()}setDefaultState(){this.searchList.instance.clearFilter("search"),this.selectedUser=void 0,this.focusedRowKey=void 0,this.roleEdit=new h,this.roleOriginal=new h,this.mode="none",this.loadSearchList()}loadSearchList(){this._roles.getAllAsDataSource().subscribe(t=>{this.rolesDataSource=t,this.rolesDataSource.load(),this.searchList.instance.refresh()})}validateAndExecute(t,i){const o=t.validationGroup.validate();this.isFormValid=o.isValid,this.isFormValid?i():console.warn("Form is not valid; action not performed.")}deleteRole(){this._roles.delete(this.roleEdit.id).subscribe(t=>{this.showToast(m.pC.success,"Success",`Role successfully deleted (${this.roleEdit.id})`),this.setDefaultState()},t=>{this.showToast(m.pC.error,"Error",`Failed to delete role (${this.roleEdit.id}): ${t.message||"Unknown error"}`)})}createRole(){this._roles.create(this.roleEdit).subscribe(t=>{this.showToast(m.pC.success,"Success",`Role successfully added (${this.roleEdit.id})`);const i=this.roleEdit;this.setDefaultState(),this.mode="roleSelected",this.roleOriginal=new h,Object.assign(this.roleOriginal,i),Object.assign(this.roleEdit,this.roleOriginal),this.usersForSelectedRole$=(0,f.of)([])},t=>{let i;console.error(t),i=409===t.status?"Role already exists with the specified Id.":`Unable to add role (${t.statusText})`,this.showToast(m.pC.error,"Error",i)})}updateRole(){this._roles.update(this.roleEdit,this.roleOriginal.id).subscribe(t=>{this.showToast(m.pC.success,"Success",`Role successfully updated (${this.roleEdit.id})`),this.rolesDataSource.store().update(this.roleEdit.id,this.roleEdit).catch(i=>{console.warn("Error updating to local DataSource: "+i),this.loadSearchList()}),Object.assign(this.roleOriginal,this.roleEdit),this.mode="roleSelected"},t=>{let i;console.error(t),i=400===t.status?`Cannot update role. Role with new Id already exists (${this.roleEdit.id}).`:`Unable to update role (Error: ${t.message})`,this.showToast(m.pC.error,"Error",i)})}showToast(t,i,o){this._toastNotificationService.show(i,o,t)}}return s.\u0275fac=function(t){return new(t||s)(e.Y36(m.fz),e.Y36(A.f),e.Y36(m.il))},s.\u0275cmp=e.Xpm({type:s,selectors:[["app-roles"]],viewQuery:function(t,i){if(1&t&&(e.Gf(S.Y,5),e.Gf(w.e,5),e.Gf(L.k,5),e.Gf(y._,5)),2&t){let o;e.iGM(o=e.CRH())&&(i.roleEditForm=o.first),e.iGM(o=e.CRH())&&(i.searchList=o.first),e.iGM(o=e.CRH())&&(i.userList=o.first),e.iGM(o=e.CRH())&&(i.userSelectBox=o.first)}},decls:33,vars:27,consts:[[3,"crumbs"],[1,"content-block-dashboard"],[1,"side-by-side"],[1,"dx-card","dx-card-w-title","content-block","responsive-paddings","left"],["id","add-role-button","icon","fa fa-plus","hint","Add role","validationGroup","validation",2,"margin-right",".5rem",3,"disabled","onClick"],["id","edit-role-button","icon","fa fa-pencil","hint","Edit selected role","validationGroup","validation",2,"margin-right",".5rem",3,"disabled","onClick"],["id","delete-role-button","icon","fa fa-trash","hint","Delete selected role","validationGroup","validation",2,"margin-right",".5rem",3,"disabled","onClick"],["icon","fa fa-refresh","hint","Refresh role list","validationGroup","validation",2,"margin-right",".5rem",3,"disabled","onClick"],["id","searchList","height","550px",3,"dataSource","showBorders","showColumnHeaders","focusedRowEnabled","hoverStateEnabled","errorRowEnabled","focusedRowKey","focusedRowKeyChange","onFocusedRowChanged","onContextMenuPreparing"],["searchList",""],["placeholder","Search...",3,"visible","width","highlightSearchText","searchVisibleColumnsOnly"],[3,"enabled"],["mode","virtual"],["mode","none"],[3,"visible"],["dataField","group",3,"groupIndex"],["dataField","id","sortOrder","asc",3,"allowSorting","sortIndex"],["dataField","description",3,"visible"],["column","group","summaryType","count","displayFormat","{0} roles"],[1,"dx-card","dx-card-w-title","content-block","responsive-paddings","right"],[1,"header"],["src","/assets/images/roles-256x256.png",1,"clip"],["class","name",4,"ngIf"],[1,"description"],[4,"ngIf"],[1,"name"],[2,"display","flex"],["id","userSelectBox","displayExpr","name","hint","Search for user to add","placeholder","Select user to add...","searchMode","contains","stylingMode","outlined",2,"flex-grow","1",3,"dataSource","selectedItem","minSearchLength","searchEnabled","searchExpr","searchTimeout","showDataBeforeSearch","showDropDownButton","disabled","selectedItemChange"],["userSelectBox",""],["id","add-user-button","icon","fa fa-plus","hint","Add selected user to role",2,"align-self","center","margin-left",".5rem",3,"disabled","onClick"],["id","userList","height","100%","keyExpr","id","itemDeleteMode","static","selectionMode","single","noDataText","No users assigned yet to this role",3,"items","allowItemDeleting","selectedItemKeys","activeStateEnabled","focusStateEnabled","onItemDeleting"],["userList",""],[4,"dxTemplate","dxTemplateOf"],["id","roleEditForm","validationGroup","validation",3,"readOnly","formData","colCount","showValidationSummary","formDataChange"],["roleEditForm",""],["dataField","id",3,"label"],["type","required","message","The role key is required"],["type","pattern","pattern","^[a-zA-Z\\d]{2,}:[a-zA-Z\\d]{2,}$","message","Group and role must be at least 2 characters long separated by a ':' and consist of only numbers and letters"],["dataField","description"],["type","required","message","The description of the role is required"],[2,"margin-top","1rem"],["text","Update","type","success","validationGroup","validation","style","margin-right: .5rem;",3,"onClick",4,"ngIf"],["text","Create","type","success","validationGroup","validation","style","margin-right: .5rem;",3,"onClick",4,"ngIf"],["text","Cancel","type","normal","validationGroup","validation",3,"onClick"],["text","Update","type","success","validationGroup","validation",2,"margin-right",".5rem",3,"onClick"],["text","Create","type","success","validationGroup","validation",2,"margin-right",".5rem",3,"onClick"]],template:function(t,i){1&t&&(e._UZ(0,"app-breadcrumbs",0),e.TgZ(1,"div",1)(2,"div",2)(3,"div",3)(4,"h1"),e._uU(5,"Roles"),e.qZA(),e.TgZ(6,"dx-button",4),e.NdJ("onClick",function(n){return i.newRoleClicked(n)}),e.qZA(),e.TgZ(7,"dx-button",5),e.NdJ("onClick",function(n){return i.roleEditClicked(n)}),e.qZA(),e.TgZ(8,"dx-button",6),e.NdJ("onClick",function(n){return i.roleDeleteClicked(n)}),e.qZA(),e.TgZ(9,"dx-button",7),e.NdJ("onClick",function(n){return i.refreshRolesClicked(n)}),e.qZA(),e.TgZ(10,"dx-data-grid",8,9),e.NdJ("focusedRowKeyChange",function(n){return i.focusedRowKey=n})("onFocusedRowChanged",function(n){return i.roleSelected(n)})("onContextMenuPreparing",function(n){return i.addMenuItems(n)}),e._UZ(12,"dxo-search-panel",10)(13,"dxo-load-panel",11)(14,"dxo-scrolling",12)(15,"dxo-sorting",13)(16,"dxo-filter-panel",14)(17,"dxi-column",15)(18,"dxi-column",16)(19,"dxi-column",17),e.TgZ(20,"dxo-summary"),e._UZ(21,"dxi-group-item",18),e.qZA()()(),e.TgZ(22,"div",19)(23,"h1"),e._uU(24),e.qZA(),e.TgZ(25,"div",20),e._UZ(26,"img",21),e.TgZ(27,"div"),e.YNc(28,V,2,1,"span",22),e.TgZ(29,"span",23),e._uU(30),e.qZA()()(),e.YNc(31,Y,11,23,"ng-container",24),e.YNc(32,W,12,8,"ng-container",24),e.qZA()()()),2&t&&(e.Q6J("crumbs",i.crumbs),e.xp6(6),e.Q6J("disabled",!i.isUserAppAdmin),e.xp6(1),e.Q6J("disabled","roleSelected"!==i.mode||!i.isUserAppAdmin||"app:admin"===(null==i.roleOriginal?null:i.roleOriginal.id.toLowerCase())),e.xp6(1),e.Q6J("disabled","roleSelected"!==i.mode||!i.isUserAppAdmin||"app:admin"===(null==i.roleOriginal?null:i.roleOriginal.id.toLowerCase())),e.xp6(1),e.Q6J("disabled","roleSelected"!==i.mode),e.xp6(1),e.Q6J("dataSource",i.rolesDataSource)("showBorders",!1)("showColumnHeaders",!1)("focusedRowEnabled",!0)("hoverStateEnabled",!0)("errorRowEnabled",!1)("focusedRowKey",i.focusedRowKey),e.xp6(2),e.Q6J("visible",!0)("width",250)("highlightSearchText",!1)("searchVisibleColumnsOnly",!1),e.xp6(1),e.Q6J("enabled",!0),e.xp6(3),e.Q6J("visible",!0),e.xp6(1),e.Q6J("groupIndex",0),e.xp6(1),e.Q6J("allowSorting",!0)("sortIndex",0),e.xp6(1),e.Q6J("visible",!1),e.xp6(5),e.Oqu(i.title),e.xp6(4),e.Q6J("ngIf",i.roleEdit),e.xp6(2),e.Oqu(i.roleEdit.description),e.xp6(1),e.Q6J("ngIf","roleSelected"===i.mode),e.xp6(1),e.Q6J("ngIf","editRole"===i.mode||"newRole"===i.mode))},dependencies:[_.O5,F.K,Q.p6,l.ZT3,S.Y,l.vrV,w.e,l.qvW,l.ecQ,l.mKI,l.PXJ,l.XXE,l.SWO,l.owF,l.X_Q,y._,L.k,k.n,_.Ov],styles:[".side-by-side[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;flex-direction:row;flex-wrap:wrap;align-items:stretch}.side-by-side[_ngcontent-%COMP%]   div.left[_ngcontent-%COMP%]{margin:6.5px;flex-shrink:1;width:300px;min-width:200px;max-width:500px}.side-by-side[_ngcontent-%COMP%]   div.right[_ngcontent-%COMP%]{margin:6.5px;flex-shrink:1;width:500px;min-width:300px}div.header[_ngcontent-%COMP%]{display:flex;font-size:2rem;align-content:flex-start;margin-bottom:2rem}div.header[_ngcontent-%COMP%]   img.clip[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;background-color:#d3d3d3}div.header[_ngcontent-%COMP%]   span.name[_ngcontent-%COMP%]{margin-left:1rem;display:block;line-height:normal}div.header[_ngcontent-%COMP%]   span.description[_ngcontent-%COMP%]{margin-left:1.1rem;font-size:medium;font-size:initial;display:block;line-height:normal}  tr.dx-row>td.dx-focused{color:#fff;background-color:#3364b8}"]}),s})();var ee=r(15861),te=r(16087),ie=r.n(te),se=r(16714),N=r(17290),oe=r(4128);class b{constructor(a){if(this.districtIds=[],this.roles=[],this.assetManagementSiteIds=[],this.remoteMonitoringSiteIds=[],this.customerAccounts=[],a)for(const[t,i]of Object.entries(a))this[t]=i}}var ne=r(80529),re=r(32451),ae=r(83582),de=r(97641),ce=r(97185),ue=r(58275),me=r(64149),he=r(68665),pe=r(52031),ge=r(76428),fe=r(23848),xe=r(59055);let Ce=(()=>{class s{transform(t){return Boolean(t?.isTeamEmployee)}}return s.\u0275fac=function(t){return new(t||s)},s.\u0275pipe=e.Yjl({name:"isTeamEmployee",type:s,pure:!0}),s})();function _e(s,a){if(1&s&&(e.TgZ(0,"span",36),e._uU(1),e.qZA()),2&s){const t=e.oxw();e.xp6(1),e.AsE(" ",t.userEdit.givenName," ",t.userEdit.surname,"")}}function ve(s,a){1&s&&(e.TgZ(0,"span"),e._uU(1,"Last Login: \xa0"),e.qZA())}function be(s,a){if(1&s&&(e.TgZ(0,"span"),e._uU(1),e.ALo(2,"amTimeAgo"),e.qZA()),2&s){const t=e.oxw();e.xp6(1),e.hij(" ",e.lcZ(2,1,t.userEdit.lastLoginDate),"")}}function Ee(s,a){1&s&&(e.TgZ(0,"p")(1,"strong")(2,"i",37),e._uU(3," Accounts with common personal email domains cannot be given permissions. Please inform the user to create a new account with their work email and retry. "),e.qZA()()())}const we=function(s,a){return{items:s,valueExpr:"value",displayExpr:"label",showSelectionControls:!0,searchEnabled:!0,applyValueMode:"useButtons",onValueChanged:a}};function Se(s,a){if(1&s&&(e.TgZ(0,"dxi-item",38),e.ALo(1,"async"),e._UZ(2,"dxo-label",39),e.qZA()),2&s){const t=e.oxw();e.Q6J("isRequired",t.isDistrictRequired)("editorOptions",e.WLB(4,we,e.lcZ(1,2,t.districts$),t.onDistrictsChanged))}}const ye=function(s){return{items:s,showSelectionControls:!0,searchEnabled:!0,applyValueMode:"useButtons"}};function Ae(s,a){if(1&s&&(e.TgZ(0,"dxi-item",40),e.ALo(1,"async"),e._UZ(2,"dxo-label",41),e.qZA()),2&s){const t=e.oxw();e.Q6J("isRequired",t.isClientRequired)("editorOptions",e.VKq(4,ye,e.lcZ(1,2,t.clients$)))}}function Te(s,a){if(1&s&&(e.TgZ(0,"dxi-item",42),e._UZ(1,"dxo-label",43),e.qZA()),2&s){const t=e.oxw();e.Q6J("isRequired",t.isAssetManagementSiteRequired)("template","assetManagementSiteIdsTemplate")}}const Re=function(){return["locationname"]};function Ue(s,a){if(1&s){const t=e.EpF();e.TgZ(0,"div")(1,"div",44)(2,"dx-tag-box",45),e.NdJ("onSelectionChanged",function(o){e.CHM(t);const n=e.oxw();return e.KtG(n.onRolesChanged(o))})("valueChange",function(o){e.CHM(t);const n=e.oxw();return e.KtG(n.userEdit.assetManagementSiteIds=o)}),e.ALo(3,"async"),e.qZA()()()}if(2&s){const t=e.oxw();e.xp6(2),e.Q6J("dataSource",e.lcZ(3,8,t.assetManagementSites$))("showSelectionControls",!0)("searchEnabled",!0)("searchExpr",e.DdM(10,Re))("displayExpr","locationname")("valueExpr","locationid")("applyValueMode","useButtons")("value",t.userEdit.assetManagementSiteIds)}}function Me(s,a){1&s&&(e.TgZ(0,"dxi-item",46),e._UZ(1,"dxo-label",47),e.qZA()),2&s&&e.Q6J("template","remoteMonitoringSiteIdsTemplate")}function Ze(s,a){if(1&s){const t=e.EpF();e.TgZ(0,"div")(1,"div",48)(2,"dx-tag-box",49),e.NdJ("onSelectionChanged",function(o){e.CHM(t);const n=e.oxw();return e.KtG(n.onRolesChanged(o))})("valueChange",function(o){e.CHM(t);const n=e.oxw();return e.KtG(n.userEdit.remoteMonitoringSiteIds=o)}),e.ALo(3,"remoteMonitoringIds"),e.ALo(4,"async"),e.qZA()()()}if(2&s){const t=e.oxw();e.xp6(2),e.Q6J("dataSource",e.lcZ(3,5,e.lcZ(4,7,t.readings$)))("showSelectionControls",!1)("searchEnabled",!0)("value",t.userEdit.remoteMonitoringSiteIds)("applyValueMode","instantly")}}function Ie(s,a){1&s&&(e.TgZ(0,"dxi-item",50),e._UZ(1,"dxo-label",51),e.qZA()),2&s&&e.Q6J("template","fieldServiceManagementContractorIdentifierTemplate")}function Oe(s,a){if(1&s){const t=e.EpF();e.TgZ(0,"div")(1,"div",52)(2,"dx-select-box",53),e.NdJ("onSelectionChanged",function(o){e.CHM(t);const n=e.oxw();return e.KtG(n.onRolesChanged(o))}),e.qZA()()()}2&s&&(e.xp6(2),e.Q6J("showSelectionControls",!1)("searchEnabled",!0))}const T=function(){return{readOnly:!0}},De=function(){return{text:"Id (Email)"}},Le=function(s,a){return{items:s,onSelectionChanged:a,showSelectionControls:!0,searchEnabled:!0,applyValueMode:"useButtons"}},Je=[{path:"users",component:(()=>{class s{constructor(t,i,o,n,g,x,E,R,U){this._users=t,this._roles=i,this._districts=o,this._customerAccounts=n,this._toasts=g,this._customerAccountsService=x,this._credoSoftService=E,this._remoteMonitoringService=R,this.ref=U,this.isFormValid=!1,this.isUserSelected=!1,this.userEdit=new b,this.userOriginal=new b,this.crumbs=[{label:"Administration",route:"/admin"},{label:"Users",route:"/admin/users"}],this.onRolesChanged=d=>{d.removedItems.map(c=>c.toLowerCase()).includes("aimaas:view")&&(this.userEdit.assetManagementSiteIds=[]),d.removedItems.map(c=>c.toLowerCase()).includes("remotemonitoring:view")&&(this.userEdit.remoteMonitoringSiteIds=[])},this.onDistrictsChanged=d=>{this.filterAssetManagementSitesByDistrict(d.value)},this.filterAssetManagementSitesByDistrict=d=>{const c=d||[];if(0===c?.length)return void this.allAssetManagementSites$.subscribe(p=>{this.assetManagementSites$=(0,f.of)(p)});const M=this.clientLocationData?.filter(p=>{const Z=p.costcentername.split(" - ")[0];return c.includes(Z)})?.map(p=>p.locationid);this.allAssetManagementSites$.pipe((0,v.U)(p=>p.filter(Z=>M?.includes(Z.locationid))))?.subscribe(p=>{this.assetManagementSites$=(0,f.of)(p)})},this.customerAccounts=function le(s){const a=["filter","searchExpr","searchOperation","searchValue","skip","take"],t=new re.Z({useDefaultSearch:!0,load:i=>{let o=new ne.LE;return a.filter(n=>n in i&&(0,ae.UE)(i[n])).forEach(n=>o=o.set(n,JSON.stringify(i[n]))),s(o)}});return new N.Z({store:t,paginate:!0,pageSize:10})}(d=>this._customerAccountsService.getCustomerAccounts(d).toPromise()),this.districts$=this._districts.getDistrictNumbers().pipe((0,v.U)(d=>d.map(c=>{const C=c.split(" - ");return{value:C[0],label:`${C[0]} - ${C[2]}`}}))),this.clients$=this._customerAccounts.getClientNames(),this.availableRoles$=this._roles.getRoleKeys(),this.currentProfile$=this._users.currentProfile$,this._credoSoftService.clientLocationData$.subscribe(d=>{this.clientLocationData=d}),this._credoSoftService.assetManagementSites$.subscribe(d=>{this.assetManagementSites$=(0,f.of)(d),this.allAssetManagementSites$=(0,f.of)(d)}),this.readings$=this._remoteMonitoringService.readings$}ngOnInit(){this.loadSearchList()}toggleCustomerAccountTooltip(){this.customerAccountsTooltipVisible=!this.customerAccountsTooltipVisible}customerDisplayExpr(t){return`${t.externalCustomer} - ${t.externalCustomerName}`}assetManagementSiteDisplayExpr(t){return`${t?.locationid} - ${t?.clientname} - ${t?.locationname}`}hasCredoViewRole(t){return t?.roles?.map(i=>i.toLowerCase()).includes("aimaas:view")}userSelected(t){t.rowIndex>=0&&(this.userOriginal=new b,Object.assign(this.userOriginal,t.row.data),this.userEdit=ie()(this.userOriginal),this.isUserSelected=!0,this.availableRolesForUser$=(0,oe.D)([this.availableRoles$,(0,f.of)(this.userOriginal.roles)]).pipe((0,v.U)(([i,o])=>Array.from(new Set([...i,...o])))),this.filterAssetManagementSitesByDistrict(this.userEdit.districtIds||[]))}userDeleteClicked(t){var i=this;return(0,ee.Z)(function*(){(yield(0,J.iG)("Are you sure you would like to delete this user from the OneInsight web portal?  This will not prevent them from logging in, but will remove all roles and permissions.","Confirm Delete"))&&i.deleteUser(t)})()}updateClicked(t){const i=t.validationGroup.validate();this.isFormValid=i.isValid,this.isFormValid&&this.updateUser()}refreshClicked(t){this.searchList.instance.clearFilter("search"),this.setDefaultFormState(t),this.loadSearchList()}cancelClicked(t){this.setDefaultFormState(t)}loadSearchList(){this._users.getAll().pipe((0,v.U)(t=>this.getDataSource(t))).subscribe(t=>{this.usersDataSource=t,this.usersDataSource.load(),this.searchList.instance.refresh()})}getDataSource(t){return new N.Z({store:new se.Z({data:t,key:"id"}),sort:"name"})}setDefaultFormState(t){this.isUserSelected=!1,this.focusedRowKey=void 0,this.userEdit=new b,this.userOriginal=new b,this.ref.detectChanges(),t&&t.validationGroup.reset()}isFieldRequired(){const t=this.userEdit.roles?.map(E=>E.toLowerCase()),i=t.includes("aimaas:district"),o=t.includes("aimaas:client"),n=t.includes("aimaas:all"),g=this.userEdit.districtIds&&this.userEdit.districtIds.length>0,x=this.userEdit.customerAccounts&&this.userEdit.customerAccounts.length>0;return!n||g&&x?i&&!g?"At least one District is required.":o&&!x?"At least one Client is required.":null:"At least one District and one Client are required."}get isAssetManagementSiteRequired(){const t=this.userEdit.roles?.map(i=>i.toLowerCase())||[];return(t.includes("aimaas:edit")||t.includes("aimaas:view"))&&!t.includes("app:admin")&&!t.includes("aimaas:admin")&&!t.includes("aimaas:all")}get isDistrictRequired(){const t=this.userEdit.roles?.map(i=>i.toLowerCase())||[];return t.includes("aimaas:district")&&!t.includes("app:admin")&&!t.includes("aimaas:admin")&&!t.includes("aimaas:all")}get isClientRequired(){const t=this.userEdit.roles?.map(i=>i.toLowerCase())||[];return t.includes("aimaas:client")&&!t.includes("app:admin")&&!t.includes("aimaas:admin")&&!t.includes("aimaas:all")}updateUser(){const t=this.userEdit.roles?.map(c=>c.toLowerCase())||[],i=t.includes("aimaas:district"),o=t.includes("aimaas:client"),n=t.includes("aimaas:all"),g=t.includes("app:admin")||t.includes("aimaas:admin"),x=t.includes("aimaas:edit"),E=t.includes("aimaas:view"),R=Array.isArray(this.userEdit.districtIds)&&this.userEdit.districtIds.length>0,U=Array.isArray(this.userEdit.customerAccounts)&&this.userEdit.customerAccounts.length>0;Number(t.includes("aimaas:all"))+Number(t.includes("aimaas:district"))+Number(t.includes("aimaas:client"))>1?this._toasts.error("Invalid selection: Only one role can be assigned. Choose either AIMaaS:ALL , AIMaas:District or AIMaas:Client ","Validation Error"):!i||R?!o||U?g||!x&&!E||this.userEdit.assetManagementSiteIds&&0!==this.userEdit.assetManagementSiteIds.length?this.userEdit.roles&&0!==this.userEdit.roles.length?!this.userEdit.isTeamEmployee&&g||!this.userEdit.isTeamEmployee&&n?this._toasts.error("Action not allowed: This role cannot be assigned to a non-TEAM Employee.","Validation Error"):this._users.update(this.userEdit,this.userOriginal.id).subscribe({next:c=>{this._toasts.success(`User successfully updated (${this.userEdit.id})`,"Success"),this.usersDataSource.store().update(this.userEdit.id,this.userEdit).catch(()=>{this.loadSearchList()})},error:c=>{401!==c.status&&this._toasts.error(400===c.status?`Cannot update user. User with new Id already exists (${this.userEdit.id}).`:`Unable to update user (Error: ${c.message})`,"Error"),console.log(c)}}):this._toasts.error("Please assign at least one role to the selected User.","Validation Error"):this._toasts.error("Please select at least one Asset Management Site ID.","Validation Error"):this._toasts.error("At least one Client is required.","Validation Error"):this._toasts.error(this.userEdit.isTeamEmployee?"At least one District is required.":"Action not allowed: This role cannot be assigned to a non-TEAM Employee.","Validation Error")}deleteUser(t){this._users.delete(this.userEdit.id).subscribe({next:i=>{this._toasts.success(`User successfully deleted (${this.userEdit.id})`,"Success"),this.usersDataSource.store().remove(this.userEdit.id).then(o=>{this.setDefaultFormState(t),this.searchList.instance.refresh()},o=>{this.loadSearchList()})},error:i=>{console.error(i),this._toasts.error(`Unable to delete user (${i.statusText})`,"Error")}})}}return s.\u0275fac=function(t){return new(t||s)(e.Y36(m.fz),e.Y36(A.f),e.Y36(m.RI),e.Y36(de.y),e.Y36(ce._W),e.Y36(A.H),e.Y36(ue._),e.Y36(me.n),e.Y36(e.sBO))},s.\u0275cmp=e.Xpm({type:s,selectors:[["app-users"]],viewQuery:function(t,i){if(1&t&&(e.Gf(S.Y,5),e.Gf(w.e,5)),2&t){let o;e.iGM(o=e.CRH())&&(i.userProfile=o.first),e.iGM(o=e.CRH())&&(i.searchList=o.first)}},decls:60,vars:93,consts:[[3,"crumbs"],[1,"content-block-dashboard"],[1,"side-by-side"],[1,"dx-card","dx-card-w-title","content-block","responsive-paddings","left"],["icon","fa fa-trash","hint","Delete selected user","validationGroup","validation",2,"margin-right",".5rem","margin-bottom",".5rem",3,"disabled","onClick"],["icon","fa fa-refresh","hint","Refresh user list","validationGroup","validation",2,"margin-right",".5rem","margin-bottom",".5rem",3,"onClick"],["id","searchList","height","550px",3,"dataSource","showBorders","showColumnHeaders","focusedRowEnabled","hoverStateEnabled","errorRowEnabled","focusedRowKey","focusedRowKeyChange","onFocusedRowChanged"],["searchList",""],["placeholder","Search...",3,"visible","width","highlightSearchText","searchVisibleColumnsOnly"],[3,"enabled"],["mode","virtual"],["mode","none"],[3,"visible"],["dataField","name","sortOrder","asc",3,"allowSorting","sortIndex"],["dataField","id",3,"visible"],[1,"dx-card","dx-card-w-title","content-block","responsive-paddings","right"],[1,"header"],["class","name",4,"ngIf"],[1,"lastLoginHeader"],[4,"ngIf"],["id","form","validationGroup","validation",3,"formData","colCount","disabled","formDataChange"],["userProfile",""],["dataField","id",3,"disabled","editorOptions","label"],["dataField","givenName",3,"disabled","editorOptions"],["dataField","surname",3,"disabled","editorOptions"],["dataField","name",3,"isRequired"],["dataField","roles","editorType","dxTagBox",3,"isRequired","editorOptions"],["dataField","districtIds","editorType","dxTagBox",3,"isRequired","editorOptions",4,"ngIf"],["dataField","customerAccounts","editorType","dxTagBox",3,"isRequired","editorOptions",4,"ngIf"],["dataField","assetManagementSiteIds",3,"isRequired","template",4,"ngIf"],[4,"dxTemplate","dxTemplateOf"],["dataField","remoteMonitoringIds",3,"template",4,"ngIf"],["dataField","fieldServiceManagementContractorIdentifier",3,"template",4,"ngIf"],["itemType","empty"],["text","Update","type","success","validationGroup","validation",2,"margin-right",".5rem",3,"disabled","onClick"],["text","Cancel","type","normal","validationGroup","validation",3,"disabled","onClick"],[1,"name"],[2,"color","red"],["dataField","districtIds","editorType","dxTagBox",3,"isRequired","editorOptions"],["text","Districts"],["dataField","customerAccounts","editorType","dxTagBox",3,"isRequired","editorOptions"],["text","Clients"],["dataField","assetManagementSiteIds",3,"isRequired","template"],["text","Asset Management Site IDs"],["id","assetManagementSiteIds"],["placeholder","No asset management site ids specified",3,"dataSource","showSelectionControls","searchEnabled","searchExpr","displayExpr","valueExpr","applyValueMode","value","onSelectionChanged","valueChange"],["dataField","remoteMonitoringIds",3,"template"],["text","Remote Monitoring IDs"],["id","remoteMonitoringSiteIds"],["placeholder","No ids specified",3,"dataSource","showSelectionControls","searchEnabled","value","applyValueMode","onSelectionChanged","valueChange"],["dataField","fieldServiceManagementContractorIdentifier",3,"template"],["text","Field Services Management Contractor"],["id","fieldServiceManagementContractorIdentifiers"],["placeholder","No contractor identifiers specified",3,"dataSource","showSelectionControls","searchEnabled","onSelectionChanged"]],template:function(t,i){if(1&t&&(e._UZ(0,"app-breadcrumbs",0),e.TgZ(1,"div",1)(2,"div",2)(3,"div",3)(4,"h1"),e._uU(5,"Users"),e.qZA(),e.TgZ(6,"dx-button",4),e.NdJ("onClick",function(n){return i.userDeleteClicked(n)}),e.qZA(),e.TgZ(7,"dx-button",5),e.NdJ("onClick",function(n){return i.refreshClicked(n)}),e.qZA(),e.TgZ(8,"dx-data-grid",6,7),e.NdJ("focusedRowKeyChange",function(n){return i.focusedRowKey=n})("onFocusedRowChanged",function(n){return i.userSelected(n)}),e._UZ(10,"dxo-search-panel",8)(11,"dxo-load-panel",9)(12,"dxo-scrolling",10)(13,"dxo-sorting",11)(14,"dxo-filter-panel",12)(15,"dxi-column",13)(16,"dxi-column",14),e.qZA()(),e.TgZ(17,"div",15)(18,"h1"),e._uU(19,"User Profile"),e.qZA(),e.TgZ(20,"div",16),e.YNc(21,_e,2,2,"span",17),e.qZA(),e.TgZ(22,"small",18),e.YNc(23,ve,2,0,"span",19),e.YNc(24,be,3,3,"span",19),e.qZA(),e.YNc(25,Ee,4,0,"p",19),e.ALo(26,"workEmail"),e.TgZ(27,"dx-form",20,21),e.NdJ("formDataChange",function(n){return i.userEdit=n}),e.ALo(29,"workEmail"),e._UZ(30,"dxi-item",22)(31,"dxi-item",23)(32,"dxi-item",24)(33,"dxi-item",25)(34,"dxi-item",26),e.ALo(35,"async"),e.YNc(36,Se,3,7,"dxi-item",27),e.ALo(37,"isTeamEmployee"),e.ALo(38,"hasRole"),e.ALo(39,"hasRole"),e.ALo(40,"hasRole"),e.YNc(41,Ae,3,6,"dxi-item",28),e.ALo(42,"hasRole"),e.ALo(43,"isTeamEmployee"),e.ALo(44,"hasRole"),e.YNc(45,Te,2,2,"dxi-item",29),e.ALo(46,"hasRole"),e.ALo(47,"hasRole"),e.YNc(48,Ue,4,11,"div",30),e.YNc(49,Me,2,1,"dxi-item",31),e.ALo(50,"hasRole"),e.YNc(51,Ze,5,9,"div",30),e.YNc(52,Ie,2,1,"dxi-item",32),e.ALo(53,"hasRole"),e.YNc(54,Oe,3,2,"div",30),e._UZ(55,"dxi-item",33),e.qZA(),e.TgZ(56,"dx-button",34),e.NdJ("onClick",function(n){return i.updateClicked(n)}),e.ALo(57,"workEmail"),e.qZA(),e.TgZ(58,"dx-button",35),e.NdJ("onClick",function(n){return i.cancelClicked(n)}),e.ALo(59,"workEmail"),e.qZA()()()()),2&t){const o=e.MAs(9);e.Q6J("crumbs",i.crumbs),e.xp6(6),e.Q6J("disabled",!o.focusedRowKey),e.xp6(2),e.Q6J("dataSource",i.usersDataSource)("showBorders",!1)("showColumnHeaders",!1)("focusedRowEnabled",!0)("hoverStateEnabled",!0)("errorRowEnabled",!1)("focusedRowKey",i.focusedRowKey),e.xp6(2),e.Q6J("visible",!0)("width",250)("highlightSearchText",!1)("searchVisibleColumnsOnly",!1),e.xp6(1),e.Q6J("enabled",!0),e.xp6(3),e.Q6J("visible",!0),e.xp6(1),e.Q6J("allowSorting",!0)("sortIndex",0),e.xp6(1),e.Q6J("visible",!1),e.xp6(5),e.Q6J("ngIf",i.userEdit),e.xp6(2),e.Q6J("ngIf",i.userEdit.lastLoginDate),e.xp6(1),e.Q6J("ngIf",i.userEdit.lastLoginDate),e.xp6(1),e.Q6J("ngIf",i.isUserSelected&&!e.lcZ(26,45,i.userEdit)),e.xp6(2),e.Q6J("formData",i.userEdit)("colCount",1)("disabled",!i.isUserSelected||!e.lcZ(29,47,i.userEdit)),e.xp6(3),e.Q6J("disabled",!0)("editorOptions",e.DdM(86,T))("label",e.DdM(87,De)),e.xp6(1),e.Q6J("disabled",!0)("editorOptions",e.DdM(88,T)),e.xp6(1),e.Q6J("disabled",!0)("editorOptions",e.DdM(89,T)),e.xp6(1),e.Q6J("isRequired",!0),e.xp6(1),e.Q6J("isRequired",!0)("editorOptions",e.WLB(90,Le,e.lcZ(35,49,i.availableRolesForUser$),i.onRolesChanged)),e.xp6(2),e.Q6J("ngIf",e.lcZ(37,51,i.userEdit)&&(e.xi3(38,53,i.userEdit,"aimaas:edit")||e.xi3(39,56,i.userEdit,"aimaas:view")||!e.xi3(40,59,i.userEdit,"aimaas:client"))),e.xp6(5),e.Q6J("ngIf",e.xi3(42,62,i.userEdit,"aimaas:all")&&e.lcZ(43,65,i.userEdit)||e.xi3(44,67,i.userEdit,"aimaas:client")),e.xp6(4),e.Q6J("ngIf",e.xi3(46,70,i.userEdit,"aimaas:view")||e.xi3(47,73,i.userEdit,"aimaas:edit")),e.xp6(3),e.Q6J("dxTemplateOf","assetManagementSiteIdsTemplate"),e.xp6(1),e.Q6J("ngIf",e.xi3(50,76,i.userEdit,"remotemonitoring:view")),e.xp6(2),e.Q6J("dxTemplateOf","remoteMonitoringSiteIdsTemplate"),e.xp6(1),e.Q6J("ngIf",e.xi3(53,79,i.userEdit,"fsm:contractor")),e.xp6(2),e.Q6J("dxTemplateOf","fieldServiceManagementContractorIdentifierTemplate"),e.xp6(2),e.Q6J("disabled",!i.isUserSelected||!e.lcZ(57,82,i.userEdit)),e.xp6(2),e.Q6J("disabled",!i.isUserSelected||!e.lcZ(59,84,i.userEdit))}},dependencies:[_.O5,F.K,Q.p6,he.U,l.ZT3,S.Y,l.sBj,w.e,l.qvW,l.ecQ,l.mKI,l.PXJ,l.XXE,l.SWO,y._,k.n,_.Ov,pe.eG,ge.j,fe.$,xe.t,Ce],styles:[".side-by-side[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;flex-direction:row;flex-wrap:wrap;align-items:stretch}.side-by-side[_ngcontent-%COMP%]   div.left[_ngcontent-%COMP%]{margin:6.5px;flex-shrink:1;width:300px;min-width:200px;max-width:500px}.side-by-side[_ngcontent-%COMP%]   div.right[_ngcontent-%COMP%]{margin:6.5px;flex-shrink:1;width:500px;min-width:300px}div.header[_ngcontent-%COMP%]{display:flex;font-size:2rem;align-content:flex-start}div.header[_ngcontent-%COMP%]   img.clip[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;background-color:#d3d3d3}div.header[_ngcontent-%COMP%]   span.name[_ngcontent-%COMP%]{margin-left:1rem;display:block}.lastLoginHeader[_ngcontent-%COMP%]{display:flex;align-content:flex-start;margin-bottom:2rem;margin-left:1.1rem}"]}),s})(),data:{pageTitle:"User Administration"},canActivate:[D.RQ,u.uy,u.W8,u.Qq],runGuardsAndResolvers:"always"},{path:"roles",component:X,data:{pageTitle:"Role Administration"},canActivate:[D.RQ,u.uy,u.W8,u.Qq],runGuardsAndResolvers:"always"},{path:"auth-history",loadChildren:()=>r.e(253).then(r.bind(r,67253)).then(s=>s.AuthHistoryModule),canLoad:[u.Sg,u.W8,u.Qq]},{path:"user-audit",loadChildren:()=>r.e(986).then(r.bind(r,60986)).then(s=>s.UserAuditModule),canLoad:[u.Sg,u.W8,u.Qq]},{path:"",redirectTo:"users",pathMatch:"full"}];let Fe=(()=>{class s{}return s.\u0275fac=function(t){return new(t||s)},s.\u0275mod=e.oAB({type:s}),s.\u0275inj=e.cJS({imports:[O.Bz.forChild(Je),O.Bz]}),s})(),Qe=(()=>{class s{}return s.\u0275fac=function(t){return new(t||s)},s.\u0275mod=e.oAB({type:s}),s.\u0275inj=e.cJS({imports:[_.ez,P.m,Fe,G.ProfileModule]}),s})()}}]);