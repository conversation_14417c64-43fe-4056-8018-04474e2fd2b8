# Azure Container Apps - Backend Environment Variables
# Complete list of variables and values needed for the Kraken backend deployment

## CORE APPLICATION SETTINGS
ASPNETCORE_ENVIRONMENT=Production
ASPNETCORE_URLS=http://+:80
AZURE_CLIENT_ID=<managed-identity-client-id>

## AZURE AD AUTHENTICATION
AzureAd__Instance=https://login.microsoftonline.com/
AzureAd__Domain=<your-azure-ad-domain>
AzureAd__TenantId=<your-azure-ad-tenant-id>
AzureAd__ClientId=<your-azure-ad-client-id>
AzureAd__ClientSecret=<secret-from-key-vault>
AzureAd__CallbackPath=/signin-oidc

## COSMOS DB CONNECTIONS
Connections__Endpoint=https://opt-<environment>-cosmosdb-001.documents.azure.com:443/
Connections__AuthKey=<cosmos-db-primary-key-from-key-vault>
Connections__DatabaseName=cosmos-clientportalapi-<environment>-001
Connections__Database=ClientPortal<Environment>
Connections__UserProfiles=user-profiles
Connections__Roles=roles
Connections__EquipmentRequests=equipment-requests
Connections__FlangeCalculations=flange-calculations
Connections__Notifications=notifications
Connections__ReleaseNotes=release-notes
Connections__AuthHistory=auth-history
Connections__KeyVaultName=kv-kraken-<environment>-001
Connections__ResourceGroupName=rg-kraken-<environment>
Connections__SubscriptionId=<azure-subscription-id>

## BLOB STORAGE CONFIGURATION
BlobStorage__BlobEndpoint=https://stakraken<environment>001.blob.core.windows.net/
BlobStorage__AccountName=stakraken<environment>001
BlobStorage__APMContainer=apm<environment>
BlobStorage__APMReportingContainer=apm<environment>-reporting
BlobStorage__APMStorageAccountName=stakraken<environment>001
BlobStorage__APMBlobContainerName=apm-<environment>
BlobStorage__APMWOStorageAccountName=stakraken<environment>001
BlobStorage__APMWOBlobContainerName=apm-workorders-<environment>
BlobStorage__KeyVaultName=kv-kraken-<environment>-001
BlobStorage__AnteaAttachmentsBlobContainer=antea-attachments-<environment>
BlobStorage__AnteaSubmissionsBlobContainer=antea-submissions-<environment>

## APPLICATION INSIGHTS
ApplicationInsights__ConnectionString=<app-insights-connection-string>

## SENDGRID EMAIL SERVICE
SendGrid__APIKey=<sendgrid-api-key-from-key-vault>

## POWER BI CONFIGURATION
PowerBI__WorkspaceId=f41e97a3-6f9f-43a5-993c-0caeeacbdc73
PowerBI__ReportId=05a80050-2764-46ce-8156-f6fb06767c72

## SWAGGER CONFIGURATION
Swagger__ClientId=<swagger-client-id-from-key-vault>
Swagger__ClientSecret=<swagger-client-secret-from-key-vault>

## FSM (FIELD SERVICE MANAGEMENT) CONFIGURATION
FSMConfig__UserName=<fsm-username-from-key-vault>
FSMConfig__Password=<fsm-password-from-key-vault>
FSMConfig__Url=<fsm-url-from-key-vault>
FSMConfig__UpdateFlowUrl=<fsm-update-flow-url-from-key-vault>

## ZDAPPER PLUS LICENSE
ZDapperPlus__LicenseName=4647;701-teaminc.com
ZDapperPlus__LicenseKey=<zdapper-license-key-from-key-vault>

## APM CONFIGURATION
APM__Environment=<environment>
APM__CmsSettings__CMSRootObjectName=Inspection
APM__CmsSettings__CMSBaseInspectionsContainerName=cms-base-inspections
APM__CmsSettings__CMSClientInspectionsContainerName=cms-client-inspections
APM__CmsSettings__CMSClientInspectionsSitesSubcontainerName=sites
APM__CmsSettings__CMSClientInspectionsSitesInspectionsSubcontainerName=inspections
APM__CmsSettings__CMSClientInspectionsAllSitesSubcontainerName=all-sites-inspections
APM__CmsSettings__CMSDistrictInspectionsContainerName=cms-district-inspections
APM__CmsSettings__CMSDistrictInspectionsSitesSubcontainerName=sites
APM__CmsSettings__CMSDistrictInspectionsSitesInspectionsSubcontainerName=inspections
APM__CmsSettings__CMSDistrictInspectionsAllSitesSubcontainerName=all-sites-inspections

## LOGGING CONFIGURATION
Logging__LogLevel__Default=Information
Logging__LogLevel__Microsoft=Warning
Logging__LogLevel__Microsoft.Hosting.Lifetime=Information
Logging__LogLevel__System=Information

## CORS CONFIGURATION
AllowedHosts=*
CorsOrigins=https://<frontend-container-app-url>

## ANTEA DATABASE CONFIGURATION
AnteaDb__ConnectionString=<antea-db-connection-string-from-key-vault>

## REMOTE MONITORING CONFIGURATION (if applicable)
ConnectionStrings__RemoteMonitoring=<remote-monitoring-connection-string>
Smartpims__Username=TEAM_Admin
Smartpims__Password=<smartpims-password-from-key-vault>

## SIGNALR CONFIGURATION (if using Azure SignalR Service)
Azure__SignalR__ConnectionString=<signalr-connection-string-from-key-vault>

## FEATURE FLAGS
FeatureManagement__RemoteMonitoring=true
FeatureManagement__EquipmentDrillDown=true
FeatureManagement__AnomalyDetection=true

## CONTAINER APP SPECIFIC SETTINGS
WEBSITES_PORT=80
WEBSITES_ENABLE_APP_SERVICE_STORAGE=false

## SECRETS THAT SHOULD BE STORED IN AZURE KEY VAULT:
# - AzureAd__ClientSecret
# - Connections__AuthKey (Cosmos DB Primary Key)
# - SendGrid__APIKey
# - Swagger__ClientId
# - Swagger__ClientSecret
# - FSMConfig__UserName
# - FSMConfig__Password
# - FSMConfig__Url
# - FSMConfig__UpdateFlowUrl
# - ZDapperPlus__LicenseKey
# - AnteaDb__ConnectionString
# - ConnectionStrings__RemoteMonitoring
# - Smartpims__Password
# - Azure__SignalR__ConnectionString
# - ApplicationInsights__ConnectionString

## ENVIRONMENT-SPECIFIC VALUES TO REPLACE:
# <environment> = dev, stg, or prod
# <managed-identity-client-id> = Client ID of the managed identity
# <your-azure-ad-domain> = Your Azure AD domain
# <your-azure-ad-tenant-id> = Your Azure AD tenant ID
# <your-azure-ad-client-id> = Your Azure AD application client ID
# <azure-subscription-id> = Your Azure subscription ID
# <frontend-container-app-url> = URL of the frontend container app
# <app-insights-connection-string> = Application Insights connection string

## DEPLOYMENT NOTES:
# 1. Use Azure Key Vault references for all sensitive values
# 2. Configure managed identity for the container app
# 3. Grant Key Vault access to the managed identity
# 4. Use environment-specific resource naming conventions
# 5. Ensure all Azure resources are created before deployment
# 6. Test connectivity to Cosmos DB, Storage Account, and Key Vault
# 7. Verify CORS settings match frontend URL
# 8. Configure health checks and monitoring
# 9. Set appropriate scaling rules
# 10. Configure ingress and networking settings

## CONTAINER APP YAML REFERENCE:
# These variables can be set in the Container App YAML under:
# properties.template.containers[0].env[]
# 
# Example:
# - name: ASPNETCORE_ENVIRONMENT
#   value: Production
# - name: AzureAd__ClientSecret
#   secretRef: azure-ad-client-secret
