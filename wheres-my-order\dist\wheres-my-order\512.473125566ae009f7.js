"use strict";(self.webpackChunkwheres_my_order=self.webpackChunkwheres_my_order||[]).push([[512],{18512:(de,C,d)=>{d.r(C),d.d(C,{RemoteMonitoringModule:()=>re});var T=d(36895),Z=d(51679),v=d(98350),R=d(15861),A=d(12739),e=d(98274),b=d(64149),S=d(63326),P=d(88e3),y=d(99106),F=d(60305),L=d(52599),J=d(94064),c=d(84943),_=d(47936),Q=d(61135),x=d(54004),D=d(83582),l=d(46611),w=d(15697),O=d(88145),k=d(16219),K=d(29338);let U=(()=>{class o{constructor(){}set selectedKeys(n){this._selectedPlants=l.gi.getPlantKeys(n),this._selectedAssets=l.gi.getAssetKeys(n),this._selectedCollectionPoints=l.gi.getCollectionPointKeys(n),this._selectedTMLs=l.gi.getTMLKeys(n)}get selectedPlants(){return this._selectedPlants??[]}get selectedAssets(){return this._selectedAssets??[]}get selectedCollectionPoints(){return this._selectedCollectionPoints??[]}get selectedTMLs(){return this._selectedTMLs??[]}}return o.\u0275fac=function(n){return new(n||o)},o.\u0275cmp=e.Xpm({type:o,selectors:[["app-selected-sensors-info"]],inputs:{selectedKeys:"selectedKeys"},decls:17,vars:4,template:function(n,t){1&n&&(e.TgZ(0,"dl")(1,"dt"),e._uU(2,"Plants selected"),e.qZA(),e.TgZ(3,"dd"),e._uU(4),e.qZA(),e.TgZ(5,"dt"),e._uU(6,"Assets selected"),e.qZA(),e.TgZ(7,"dd"),e._uU(8),e.qZA(),e.TgZ(9,"dt"),e._uU(10,"Collection points selected"),e.qZA(),e.TgZ(11,"dd"),e._uU(12),e.qZA(),e.TgZ(13,"dt"),e._uU(14,"TMLs selected"),e.qZA(),e.TgZ(15,"dd"),e._uU(16),e.qZA()()),2&n&&(e.xp6(4),e.Oqu(t.selectedPlants.join(", ")),e.xp6(4),e.Oqu(t.selectedAssets.join(", ")),e.xp6(4),e.Oqu(t.selectedCollectionPoints.join(", ")),e.xp6(4),e.Oqu(t.selectedTMLs.join(", ")))},styles:["dt[_ngcontent-%COMP%]{font-weight:700}dd[_ngcontent-%COMP%]{margin-bottom:1em}"]}),o})(),I=(()=>{class o{transform(n,t){let i;const s=t.filter(r=>4===r.split("->").length);return i=t?.length>0?n.filter(r=>s.some(m=>l.gi.keyMatches(m,r))):n,i?.length>0?Math.min(...i.map(r=>r.thickness)).toFixed(4):"-"}}return o.\u0275fac=function(n){return new(n||o)},o.\u0275pipe=e.Yjl({name:"minThickness",type:o,pure:!0}),o})(),N=(()=>{class o{transform(n,t){let i;const s=t.filter(m=>4===m.split("->").length);i=t?.length>0?n.filter(m=>s.some(p=>l.gi.keyMatches(p,m))):n;const r=i.sort((m,p)=>m.thickness-p.thickness);return r.length>1?r[0].asset+", "+r[0].collectionPoint+", "+r[0].tml:""}}return o.\u0275fac=function(n){return new(n||o)},o.\u0275pipe=e.Yjl({name:"minSensorData",type:o,pure:!0}),o})(),$=(()=>{class o{transform(n,t){if(0===t.length)return[null,null];let i;const s=t.filter(p=>4===p.split("->").length);return i=t?.length>0?n.filter(p=>s.some(h=>l.gi.keyMatches(h,p))):n,[i?.length>0?Math.min(...i.map(p=>p.thickness-.1)).toFixed(4):null,i?.length>0?Math.max(...i.map(p=>p.thickness+.1)).toFixed(4):null]}}return o.\u0275fac=function(n){return new(n||o)},o.\u0275pipe=e.Yjl({name:"thicknessRange",type:o,pure:!0}),o})(),j=(()=>{class o{transform(n,t){if(0===t.length)return[null,null];let i;const s=t.filter(u=>4===u.split("->").length);if(i=t?.length>0?n.filter(u=>s.some(le=>l.gi.keyMatches(le,u))):n,0===i.length)return[null,null];const r=Math.min(...i.map(u=>u.corrosionRateLongTerm))-.1,m=Math.max(...i.map(u=>u.corrosionRateLongTerm))+.1,p=Math.min(...i.map(u=>u.corrosionRateShortTerm))-.1,h=Math.max(...i.map(u=>u.corrosionRateShortTerm))+.1;return[Math.min(r,p).toFixed(4),Math.max(m,h).toFixed(4)]}}return o.\u0275fac=function(n){return new(n||o)},o.\u0275pipe=e.Yjl({name:"corrosionRateRange",type:o,pure:!0}),o})(),z=(()=>{class o{transform(n,t){if(0===t.length)return[null,null];let i;const s=t.filter(g=>4===g.split("->").length);if(i=t?.length>0?n.filter(g=>s.some(f=>l.gi.keyMatches(f,g))):n,0===i.length)return[null,null];const r=Math.min(...i.map(g=>g.materialTemperature)),m=Math.max(...i.map(g=>g.materialTemperature));let p;const h=.25*(m-r);return p=h>10?h:10,[(r-p).toFixed(0),(m+p).toFixed(0)]}}return o.\u0275fac=function(n){return new(n||o)},o.\u0275pipe=e.Yjl({name:"temperatureRange",type:o,pure:!0}),o})();function Y(o,a){if(1&o){const n=e.EpF();e.TgZ(0,"div",27)(1,"dx-tree-view",28),e.NdJ("onSelectionChanged",function(i){e.CHM(n);const s=e.oxw();return e.KtG(s.onSelectionChanged(i))}),e.ALo(2,"async"),e.qZA()()}if(2&o){const n=e.oxw();e.xp6(1),e.Q6J("items",e.lcZ(2,5,n.items$))("showCheckBoxesMode","normal")("selectionMode","multiple")("selectNodesRecursive",!0)("selectByClick",!1)}}function B(o,a){if(1&o&&e._UZ(0,"dxi-series",29),2&o){const n=a.$implicit;e.Q6J("valueField",n+"Thickness")("name","Thickness - "+n)("axis","thickness")}}function E(o,a){if(1&o&&e._UZ(0,"dxi-series",29),2&o){const n=a.$implicit;e.Q6J("valueField",n+"MaterialTemperature")("name","Material Temperature - "+n)("axis","temperature")}}function W(o,a){if(1&o&&e._UZ(0,"dxi-series",30),2&o){const n=a.$implicit;e.Q6J("name","Corrosion Rate (Long Term) - "+n)("valueField",n+"CorrosionRateLT")("axis","corrosion")}}function G(o,a){if(1&o&&e._UZ(0,"dxi-series",30),2&o){const n=a.$implicit;e.Q6J("name","Corrosion Rate (Short Term) - "+n)("valueField",n+"CorrosionRateST")("axis","corrosion")}}function q(o,a){if(1&o&&e._UZ(0,"dxi-series",29),2&o){const n=a.$implicit;e.Q6J("valueField",n+"MaterialTemperature")("name","Material Temperature - "+n)("axis","temperature")}}const M=function(){return{type:"fixedPoint",precision:3}};let X=(()=>{class o{constructor(n){this._sensors=n,this._selectedKeys=new Q.X([]),this.isDrawerOpen=!0,this.selectedTMLs$=this.selectedKeys$.pipe((0,x.U)(t=>l.gi.getTMLKeys(t))),this.readings=[],this.readings$=this.selectedKeys$.pipe((0,x.U)(t=>{const i=[];return this.readings.forEach(s=>{t.some(r=>l.gi.keyMatches(r,s))&&i.push({...s,[s.tml+"Thickness"]:s.thickness,[s.tml+"MaterialTemperature"]:s.materialTemperature,[s.tml+"CorrosionRateLT"]:s.corrosionRateLongTerm,[s.tml+"CorrosionRateST"]:s.corrosionRateShortTerm})}),i})),this.selectByClick=!1,this.customizeThicknessTemperatureTooltip=t=>{const i=t.seriesName.split(" - "),s="Thickness"===i[0],r=i[1],m=new Date(t.argument);return{text:r+"<br/>"+m.toLocaleDateString()+" "+m.toLocaleTimeString()+"<br/>"+t.valueText+(s?" in":" &deg;F")}},this.customizeCorrosionRateTemperatureTooltip=t=>{const i=t.seriesName.split(" - "),s=i[0].startsWith("Corrosion Rate"),r=i[1],m=new Date(t.argument);return{text:r+"<br/>"+m.toLocaleDateString()+" "+m.toLocaleTimeString()+"<br/>"+t.valueText+(s?" in/yr":" &deg;F")}}}get selectedKeys$(){return this._selectedKeys.asObservable()}ngOnInit(){this.items$=this._sensors.selectedReadings$.pipe((0,x.U)(t=>this.getDataSource(t))),this._sensors.selectedReadings$.subscribe(t=>{this.readings=t}),this._observer=(0,D.Rn)(()=>{this.charts.forEach(t=>t.instance.render())});const n=document.getElementById("remote-monitoring-dashboard");n&&this._observer.observe(n)}ngOnDestroy(){this._observer.disconnect(),this._observer=null}onSelectionChanged(n){const t=n.component.getSelectedNodeKeys();this._selectedKeys.next(t)}resetZoom(n){n.instance.resetVisualRange()}legendClick(n){const t=n.target;t.isVisible()?t.hide():t.show()}getDataSource(n){const t=[];for(const i of n)t.some(s=>s.id===l.iT.plantId(i))||t.push(new l.iT(l.iT.plantId(i),i.plant)),t.some(s=>s.id===l.iT.assetId(i)&&s.parentId===l.iT.plantId(i))||t.push(new l.iT(l.iT.assetId(i),i.asset,l.iT.plantId(i))),t.some(s=>s.id===l.iT.collectionPointId(i)&&s.parentId===l.iT.assetId(i))||t.push(new l.iT(l.iT.collectionPointId(i),i.collectionPoint,l.iT.assetId(i))),t.some(s=>s.id===l.iT.tmlId(i)&&s.parentId===l.iT.collectionPointId(i))||t.push(new l.iT(l.iT.tmlId(i),i.tml,l.iT.collectionPointId(i)));return t}}return o.\u0275fac=function(n){return new(n||o)(e.Y36(b.n))},o.\u0275cmp=e.Xpm({type:o,selectors:[["app-dashboard-tab"]],viewQuery:function(n,t){if(1&n&&e.Gf(_.q,5),2&n){let i;e.iGM(i=e.CRH())&&(t.charts=i)}},decls:65,vars:96,consts:[[2,"margin-bottom","1rem",3,"icon","text","stylingMode","onClick"],["template","template",3,"openedStateMode","position","revealMode","opened","openedChange"],["style","width: 300px; background-color: #f7f7f7; padding: 2em",4,"dxTemplate","dxTemplateOf"],["id","remote-monitoring-dashboard",1,"responsive-paddings"],[2,"display","flex","justify-content","space-between","width","100%"],[1,"dx-fieldset",2,"flex","1"],[3,"selectedKeys"],[2,"padding-left","1rem","flex","1","display","flex"],[2,"margin","auto",3,"label","value","subtext"],["text","Reset",3,"onClick"],["palette","Violet",3,"dataSource","onLegendClick"],["thicknessTemperatureChart",""],[3,"valueField","name","axis",4,"ngFor","ngForOf"],[3,"argumentType"],[3,"visualRange","name"],[3,"text"],[3,"format"],[3,"visualRange","name","position"],[3,"argumentField"],[3,"size"],["argumentAxis","both",3,"dragToZoom","allowMouseWheel"],["verticalAlignment","top","horizontalAlignment","right","position","outside"],["horizontalAlignment","center","text","Thickness, Temperature Over Time"],[3,"enabled","customizeTooltip"],["corrosionChart",""],[3,"name","valueField","axis",4,"ngFor","ngForOf"],["horizontalAlignment","center","text","Corrosion Rate Over Time"],[2,"width","300px","background-color","#f7f7f7","padding","2em"],["dataStructure","plain",3,"items","showCheckBoxesMode","selectionMode","selectNodesRecursive","selectByClick","onSelectionChanged"],[3,"valueField","name","axis"],[3,"name","valueField","axis"]],template:function(n,t){if(1&n){const i=e.EpF();e.TgZ(0,"dx-button",0),e.NdJ("onClick",function(){return t.isDrawerOpen=!t.isDrawerOpen}),e.qZA(),e.TgZ(1,"dx-drawer",1),e.NdJ("openedChange",function(r){return t.isDrawerOpen=r}),e.YNc(2,Y,3,7,"div",2),e.TgZ(3,"div",3)(4,"section",4)(5,"div",5),e._UZ(6,"app-selected-sensors-info",6),e.ALo(7,"async"),e.qZA(),e.TgZ(8,"div",7),e._UZ(9,"app-overview-box",8),e.ALo(10,"minThickness"),e.ALo(11,"async"),e.ALo(12,"minSensorData"),e.ALo(13,"async"),e.qZA()(),e.TgZ(14,"dx-button",9),e.NdJ("onClick",function(){e.CHM(i);const r=e.MAs(17);return e.KtG(t.resetZoom(r))}),e.qZA(),e.TgZ(15,"section")(16,"dx-chart",10,11),e.NdJ("onLegendClick",function(r){return t.legendClick(r)}),e.ALo(18,"async"),e.YNc(19,B,1,3,"dxi-series",12),e.ALo(20,"async"),e.YNc(21,E,1,3,"dxi-series",12),e.ALo(22,"async"),e._UZ(23,"dxo-argument-axis",13),e.TgZ(24,"dxi-value-axis",14),e.ALo(25,"thicknessRange"),e.ALo(26,"async"),e._UZ(27,"dxo-title",15)(28,"dxo-label",16),e.qZA(),e.TgZ(29,"dxi-value-axis",17),e.ALo(30,"temperatureRange"),e.ALo(31,"async"),e._UZ(32,"dxo-title",15),e.qZA(),e.TgZ(33,"dxo-common-series-settings",18),e._UZ(34,"dxo-point",19),e.qZA(),e._UZ(35,"dxo-zoom-and-pan",20)(36,"dxo-legend",21)(37,"dxo-title",22)(38,"dxo-tooltip",23),e.qZA()(),e.TgZ(39,"dx-button",9),e.NdJ("onClick",function(){e.CHM(i);const r=e.MAs(42);return e.KtG(t.resetZoom(r))}),e.qZA(),e.TgZ(40,"section")(41,"dx-chart",10,24),e.NdJ("onLegendClick",function(r){return t.legendClick(r)}),e.ALo(43,"async"),e.YNc(44,W,1,3,"dxi-series",25),e.ALo(45,"async"),e.YNc(46,G,1,3,"dxi-series",25),e.ALo(47,"async"),e.YNc(48,q,1,3,"dxi-series",12),e.ALo(49,"async"),e.TgZ(50,"dxi-value-axis",14),e.ALo(51,"corrosionRateRange"),e.ALo(52,"async"),e._UZ(53,"dxo-title",15)(54,"dxo-label",16),e.qZA(),e.TgZ(55,"dxi-value-axis",17),e.ALo(56,"temperatureRange"),e.ALo(57,"async"),e._UZ(58,"dxo-title",15),e.qZA(),e.TgZ(59,"dxo-common-series-settings",18),e._UZ(60,"dxo-point",19),e.qZA(),e._UZ(61,"dxo-zoom-and-pan",20)(62,"dxo-legend",21)(63,"dxo-title",26)(64,"dxo-tooltip",23),e.qZA()()()()}2&n&&(e.Q6J("icon",t.isDrawerOpen?"chevronleft":"filter")("text",t.isDrawerOpen?"Collapse":"Filter")("stylingMode","text"),e.xp6(1),e.Q6J("openedStateMode","shrink")("position","left")("revealMode","slide")("opened",t.isDrawerOpen),e.xp6(1),e.Q6J("dxTemplateOf","template"),e.xp6(4),e.Q6J("selectedKeys",e.lcZ(7,48,t.selectedKeys$)),e.xp6(3),e.Q6J("label","Minimum Thickness")("value",e.xi3(10,50,t.readings,e.lcZ(11,53,t.selectedKeys$)))("subtext",e.xi3(12,55,t.readings,e.lcZ(13,58,t.selectedKeys$))),e.xp6(7),e.Q6J("dataSource",e.lcZ(18,60,t.readings$)),e.xp6(3),e.Q6J("ngForOf",e.lcZ(20,62,t.selectedTMLs$)),e.xp6(2),e.Q6J("ngForOf",e.lcZ(22,64,t.selectedTMLs$)),e.xp6(2),e.Q6J("argumentType","datetime"),e.xp6(1),e.Q6J("visualRange",e.xi3(25,66,t.readings,e.lcZ(26,69,t.selectedKeys$)))("name","thickness"),e.xp6(3),e.Q6J("text","Thickness"),e.xp6(1),e.Q6J("format",e.DdM(94,M)),e.xp6(1),e.Q6J("visualRange",e.xi3(30,71,t.readings,e.lcZ(31,74,t.selectedKeys$)))("name","temperature")("position","right"),e.xp6(3),e.Q6J("text","Temperature"),e.xp6(1),e.Q6J("argumentField","dateTimeUTC"),e.xp6(1),e.Q6J("size",3),e.xp6(1),e.Q6J("dragToZoom",!0)("allowMouseWheel",!1),e.xp6(3),e.Q6J("enabled",!0)("customizeTooltip",t.customizeThicknessTemperatureTooltip),e.xp6(3),e.Q6J("dataSource",e.lcZ(43,76,t.readings$)),e.xp6(3),e.Q6J("ngForOf",e.lcZ(45,78,t.selectedTMLs$)),e.xp6(2),e.Q6J("ngForOf",e.lcZ(47,80,t.selectedTMLs$)),e.xp6(2),e.Q6J("ngForOf",e.lcZ(49,82,t.selectedTMLs$)),e.xp6(2),e.Q6J("visualRange",e.xi3(51,84,t.readings,e.lcZ(52,87,t.selectedKeys$)))("name","corrosion"),e.xp6(3),e.Q6J("text","Corrosion"),e.xp6(1),e.Q6J("format",e.DdM(95,M)),e.xp6(1),e.Q6J("visualRange",e.xi3(56,89,t.readings,e.lcZ(57,92,t.selectedKeys$)))("name","temperature")("position","right"),e.xp6(3),e.Q6J("text","Temperature"),e.xp6(1),e.Q6J("argumentField","dateTimeUTC"),e.xp6(1),e.Q6J("size",3),e.xp6(1),e.Q6J("dragToZoom",!0)("allowMouseWheel",!1),e.xp6(3),e.Q6J("enabled",!0)("customizeTooltip",t.customizeCorrosionRateTemperatureTooltip))},dependencies:[T.sg,w.K,y.p6,c.sBj,O.U,k.u,c.P_1,c.Akp,c.bcN,c.POg,_.q,c.NwO,c.T87,c.v6i,c.UQn,c.e3c,K.V,U,T.Ov,I,N,$,j,z],styles:["section[_ngcontent-%COMP%]{margin-bottom:1.5rem}"]}),o})();function V(o,a){1&o&&(e.TgZ(0,"div",4),e._UZ(1,"app-dashboard-tab"),e.qZA())}const H=function(){return{mode:"multiple"}},ee=function(){return[5,10,25,50]};function te(o,a){if(1&o){const n=e.EpF();e.TgZ(0,"div",4)(1,"dx-data-grid",5,6),e.NdJ("onToolbarPreparing",function(i){e.CHM(n);const s=e.oxw();return e.KtG(s.onToolbarPreparing(i))}),e._UZ(3,"dxo-group-panel",7)(4,"dxo-scrolling",8)(5,"dxo-paging",9)(6,"dxo-pager",10)(7,"dxo-header-filter",7)(8,"dxo-filter-row",7)(9,"dxo-search-panel",11)(10,"dxo-filter-panel",7)(11,"dxo-column-chooser",12)(12,"dxo-export",13)(13,"dxo-state-storing",14)(14,"dxi-column",15)(15,"dxi-column",16)(16,"dxi-column",17)(17,"dxi-column",18)(18,"dxi-column",19)(19,"dxi-column",20)(20,"dxi-column",21)(21,"dxi-column",22)(22,"dxi-column",23)(23,"dxi-column",24)(24,"dxi-column",25)(25,"dxi-column",26)(26,"dxi-column",27)(27,"dxi-column",28)(28,"dxi-column",29)(29,"dxi-column",30)(30,"dxi-column",31)(31,"dxi-column",32)(32,"dxi-column",33)(33,"dxi-column",34)(34,"dxi-column",35)(35,"dxi-column",36)(36,"dxi-column",37),e.qZA()()}if(2&o){const n=e.oxw();e.xp6(1),e.Q6J("allowColumnReordering",!0)("allowColumnResizing",!0)("columnAutoWidth",!0)("dataSource",n.readings)("selection",e.DdM(20,H)),e.xp6(2),e.Q6J("visible",!0),e.xp6(1),e.Q6J("useNative",!0),e.xp6(1),e.Q6J("pageSize",5),e.xp6(1),e.Q6J("showPageSizeSelector",!0)("visible",!0)("allowedPageSizes",e.DdM(21,ee)),e.xp6(1),e.Q6J("visible",!0),e.xp6(1),e.Q6J("visible",!0),e.xp6(1),e.Q6J("visible",!0)("highlightCaseSensitive",!0),e.xp6(1),e.Q6J("visible",!0),e.xp6(1),e.Q6J("enabled",!0),e.xp6(1),e.Q6J("enabled",!0)("allowExportSelectedData",!0),e.xp6(1),e.Q6J("enabled",!0)}}const ne=function(){return{title:"Dashboard",template:"dashboard-tab"}},oe=function(){return{title:"Data Detail",template:"data-detail-tab"}},ie=function(o,a){return[o,a]},se=[{path:"",component:(()=>{class o{constructor(n,t,i){this._sensors=n,this._grid=t,this._remoteMonitoringIdsPipe=i,this.tabItems=["Dashboard","Data Detail"],this.projects=[],this.readings=[],this._sensors.readings$.pipe((0,A.fF)("sensors")).subscribe(s=>{this.projects=this._remoteMonitoringIdsPipe.transform(s),this.projects.includes(this.selectedProject)||(this.selectedProject=this.projects[0])})}ngOnInit(){this._sensors.selectedReadings$.subscribe(n=>{this.readings=n})}onToolbarPreparing(n){var s,t=this;n.toolbarOptions.items.unshift({widget:"dxButton",options:{icon:"fa fa-undo",hint:"Restore Grid Defaults",onClick:(s=(0,R.Z)(function*(){(yield t._grid.resetGridState(n.component))&&localStorage.removeItem("sensorsGridState")}),function(){return s.apply(this,arguments)})},location:"after"})}changeProject(n){this.selectedProject=n.selectedItem,this._sensors.selectProject(this.selectedProject)}}return o.\u0275fac=function(n){return new(n||o)(e.Y36(b.n),e.Y36(S.A6),e.Y36(P.th))},o.\u0275cmp=e.Xpm({type:o,selectors:[["app-remote-monitoring"]],decls:5,vars:11,consts:[[1,"responsive-paddings","dx-card","content-block"],[3,"items","value","width","valueChange","onSelectionChanged"],[2,"margin-top","25px",3,"items"],["class","responsive-paddings",4,"dxTemplate","dxTemplateOf"],[1,"responsive-paddings"],[3,"allowColumnReordering","allowColumnResizing","columnAutoWidth","dataSource","selection","onToolbarPreparing"],["sensors",""],[3,"visible"],[3,"useNative"],[3,"pageSize"],[3,"showPageSizeSelector","visible","allowedPageSizes"],[3,"visible","highlightCaseSensitive"],["mode","dragAndDrop",3,"enabled"],[3,"enabled","allowExportSelectedData"],["type","localStorage","storageKey","sensorsGridState",3,"enabled"],["dataField","dateTimeUTC","dataType","datetime","caption","Date Time UTC"],["dataField","thicknessAlarmState","dataType","string","caption","Thickness Alarm State"],["dataField","corrosionAlarmState","dataType","string","caption","Corrosion Alarm State"],["dataField","thickness","dataType","number"],["dataField","tempCompensatedThickness","dataType","number","caption","Temp Compensated Thickness"],["dataField","materialTemperature","dataType","number"],["dataField","dsiTemperature","dataType","number","caption","DSI Temperature"],["dataField","referenceVelocity","dataType","number"],["dataField","tempCompensatedVelocity","dataType","number","caption","Temp Compensated Velocity"],["dataField","corrosionRateShortTerm","dataType","number","caption","Corrosion Rate Short Term"],["dataField","endOfLifeShortTerm","dataType","date","caption","End Of Life Short Term"],["dataField","corrosionRateLongTerm","dataType","number","caption","Corrosion Rate Long Term"],["dataField","endOfLifeLongTerm","dataType","date","caption","End Of Life Long Term"],["dataField","thicknessAlarmThreshold","dataType","number","caption","Thickness Alarm Threshold"],["dataField","thicknessWarningThreshold","dataType","number","caption","Thickness Warning Threshold"],["dataField","corrosionAlarmThreshold","dataType","number","caption","Corrosion Alarm Threshold"],["dataField","corrosionWarningThreshold","dataType","number","caption","Corrosion Warning Threshold"],["dataField","company","dataType","string"],["dataField","site","dataType","string"],["dataField","plant","dataType","string"],["dataField","asset","dataType","string"],["dataField","collectionPoint","dataType","string"],["dataField","tml","dataType","string","caption","TML"]],template:function(n,t){1&n&&(e.TgZ(0,"div",0)(1,"dx-select-box",1),e.NdJ("valueChange",function(s){return t.selectedProject=s})("onSelectionChanged",function(s){return t.changeProject(s)}),e.qZA(),e.TgZ(2,"dx-tab-panel",2),e.YNc(3,V,2,0,"div",3),e.YNc(4,te,37,22,"div",3),e.qZA()()),2&n&&(e.xp6(1),e.Q6J("items",t.projects)("value",t.selectedProject)("width",200),e.xp6(1),e.Q6J("items",e.WLB(8,ie,e.DdM(6,ne),e.DdM(7,oe))),e.xp6(1),e.Q6J("dxTemplateOf","dashboard-tab"),e.xp6(1),e.Q6J("dxTemplateOf","data-detail-tab"))},dependencies:[y.p6,F.e,c.Auv,c.qvW,c.tZE,c.Ak0,c.ecQ,c.I62,c.Een,c.ilc,c.sXh,c.PXJ,c.XXE,c.C9T,L._,J.I,X],styles:["[_nghost-%COMP%]{width:100%}[_nghost-%COMP%]   .dx-field-label[_ngcontent-%COMP%]{width:10%}[_nghost-%COMP%]   .dx-field-value[_ngcontent-%COMP%]:not(.dx-switch):not(.dx-checkbox):not(.dx-button){width:90%}[_nghost-%COMP%]   section[_ngcontent-%COMP%]{margin-bottom:1.5rem}"]}),o})(),data:{pageTitle:"Remote Asset Monitoring"}}];let ae=(()=>{class o{}return o.\u0275fac=function(n){return new(n||o)},o.\u0275mod=e.oAB({type:o}),o.\u0275inj=e.cJS({imports:[v.Bz.forChild(se),v.Bz]}),o})(),re=(()=>{class o{}return o.\u0275fac=function(n){return new(n||o)},o.\u0275mod=e.oAB({type:o}),o.\u0275inj=e.cJS({imports:[T.ez,ae,Z.m]}),o})()}}]);