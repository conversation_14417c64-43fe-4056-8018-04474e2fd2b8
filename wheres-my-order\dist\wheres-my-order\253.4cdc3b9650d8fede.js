"use strict";(self.webpackChunkwheres_my_order=self.webpackChunkwheres_my_order||[]).push([[253],{67253:(ae,x,a)=>{a.r(x),a.d(x,{AuthHistoryModule:()=>re});var c=a(36895),Z=a(51679),y=a(98350),O=a(61978),H=a(47259),S=a(15861),J=a(5128),U=a(94327),I=a(34782),N=a(70262),Q=a(92340);const p=n=>n instanceof Date,f=n=>0===Object.keys(n).length,s=n=>null!=n&&"object"==typeof n,g=(n,...o)=>Object.prototype.hasOwnProperty.call(n,...o),h=n=>s(n)&&f(n),v=()=>Object.create(null),C=(n,o)=>n!==o&&s(n)&&s(o)?Object.keys(o).reduce((t,i)=>{if(g(n,i)){const r=C(n[i],o[i]);return s(r)&&f(r)||(t[i]=r),t}return t[i]=o[i],t},v()):{},F=C,A=(n,o)=>n!==o&&s(n)&&s(o)?Object.keys(n).reduce((t,i)=>{if(g(o,i)){const r=A(n[i],o[i]);return s(r)&&f(r)||(t[i]=r),t}return t[i]=void 0,t},v()):{},j=A,_=(n,o)=>n===o?{}:s(n)&&s(o)?p(n)||p(o)?n.valueOf()==o.valueOf()?{}:o:Object.keys(o).reduce((t,i)=>{if(g(n,i)){const r=_(n[i],o[i]);return h(r)&&!p(r)&&(h(n[i])||!h(o[i]))||(t[i]=r),t}return t},v()):o,E=_;class w{static diff(o,t){return((n,o)=>({added:F(n,o),deleted:j(n,o),updated:E(n,o)}))(o,t)}}var e=a(98274),P=a(80529),M=a(99106),u=a(84943),R=a(60305),b=a(87074),D=a(64564),V=a(81142),z=a(82995);let B=(()=>{class n{transform(t){return w.diff(t.old,t.new)}}return n.\u0275fac=function(t){return new(t||n)},n.\u0275pipe=e.Yjl({name:"diff",type:n,pure:!0}),n})();const G=["historyGrid"],Y=function(n,o){return[n,o]};function K(n,o){if(1&n){const t=e.EpF();e.TgZ(0,"dx-range-selector",17),e.NdJ("onInitialized",function(r){e.CHM(t);const d=e.oxw(2);return e.KtG(d.onRangeSelectorInitialized(r))})("onValueChanged",function(r){e.CHM(t);const d=e.oxw(2);return e.KtG(d.onRangeSelectorValueChanged(r))}),e._UZ(1,"dxo-scale",18)(2,"dxo-slider-marker",19),e.qZA()}if(2&n){const t=e.oxw(2);e.Q6J("value",e.WLB(3,Y,t.valueStart,t.valueEnd)),e.xp6(1),e.Q6J("startValue",t.rangeStart)("endValue",t.rangeEnd)}}function q(n,o){if(1&n&&(e.TgZ(0,"li")(1,"strong"),e._uU(2),e.qZA(),e._uU(3,": "),e.TgZ(4,"span"),e._uU(5),e.ALo(6,"json"),e.qZA()()),2&n){const t=o.$implicit;e.xp6(2),e.Oqu(t.key),e.xp6(3),e.Oqu(e.lcZ(6,2,t.value))}}function W(n,o){if(1&n&&(e.TgZ(0,"div")(1,"h4"),e._uU(2),e.qZA(),e.TgZ(3,"ul"),e.YNc(4,q,7,4,"li",23),e.ALo(5,"keyvalue"),e.qZA()()),2&n){const t=o.$implicit;e.xp6(2),e.Oqu(t.key),e.xp6(2),e.Q6J("ngForOf",e.lcZ(5,2,t.value))}}function X(n,o){if(1&n&&(e.TgZ(0,"div"),e.YNc(1,W,6,4,"div",23),e.ALo(2,"keyvalue"),e.qZA()),2&n){const t=o.ngIf;e.xp6(1),e.Q6J("ngForOf",e.lcZ(2,1,t))}}function $(n,o){if(1&n&&(e.TgZ(0,"div")(1,"dx-scroll-view")(2,"p"),e._uU(3," Change made by: "),e.TgZ(4,"span"),e._uU(5),e.qZA()(),e.TgZ(6,"p"),e._uU(7," Change made at: "),e.TgZ(8,"span"),e._uU(9),e.ALo(10,"date"),e.qZA()(),e.TgZ(11,"div",20)(12,"div",21)(13,"h4"),e._uU(14,"Old"),e.qZA(),e.TgZ(15,"pre"),e._uU(16),e.ALo(17,"json"),e.qZA()(),e.TgZ(18,"div",22)(19,"h4"),e._uU(20,"New"),e.qZA(),e.TgZ(21,"pre"),e._uU(22),e.ALo(23,"json"),e.qZA()()(),e.YNc(24,X,3,3,"div",0),e.ALo(25,"diff"),e.qZA()()),2&n){const t=e.oxw(2);e.xp6(5),e.Oqu(null!=t.currentChange&&null!=t.currentChange.user&&t.currentChange.user.email?t.currentChange.user.email:t.currentChange.user.id),e.xp6(4),e.Oqu(e.xi3(10,5,t.currentChange.createdAt,"medium")),e.xp6(7),e.Oqu(e.lcZ(17,8,t.currentChange.old)),e.xp6(6),e.Oqu(null!==t.currentChange.new?e.lcZ(23,10,t.currentChange.new):"User has been deleted"),e.xp6(2),e.Q6J("ngIf",e.lcZ(25,12,t.currentChange))}}function k(n,o){if(1&n){const t=e.EpF();e.TgZ(0,"div")(1,"div",2),e.YNc(2,K,3,6,"dx-range-selector",3),e.TgZ(3,"dx-data-grid",4,5),e.NdJ("onToolbarPreparing",function(r){e.CHM(t);const d=e.oxw();return e.KtG(d.onToolbarPreparing(r))}),e._UZ(5,"dxo-selection",6)(6,"dxo-search-panel",7)(7,"dxo-filter-panel",8)(8,"dxi-column",9)(9,"dxi-column",10)(10,"dxi-column",11)(11,"dxi-column",12),e.TgZ(12,"dxi-column",13),e._UZ(13,"dxi-button",14),e.qZA()()(),e.TgZ(14,"dx-popup",15),e.NdJ("visibleChange",function(r){e.CHM(t);const d=e.oxw();return e.KtG(d.showDiff=r)}),e.YNc(15,$,26,14,"div",16),e.qZA()()}if(2&n){const t=e.oxw();e.xp6(2),e.Q6J("ngIf",t.valueStart&&t.valueEnd&&t.rangeStart&&t.rangeEnd),e.xp6(1),e.Q6J("dataSource",t.selected),e.xp6(3),e.Q6J("visible",!0),e.xp6(1),e.Q6J("visible",!0),e.xp6(1),e.Q6J("allowSorting",!0)("allowSearch",!0)("calculateCellValue",t.calculateUserId),e.xp6(3),e.Q6J("allowSorting",!0)("allowSearch",!0)("calculateCellValue",t.calculateId),e.xp6(1),e.Q6J("width",110),e.xp6(1),e.Q6J("onClick",t.diffClicked),e.xp6(1),e.Q6J("visible",t.showDiff)("showCloseButton",!0)("hideOnOutsideClick",!0),e.xp6(1),e.Q6J("dxTemplateOf","content")}}function ee(n,o){1&n&&(e.TgZ(0,"div",24),e._UZ(1,"dx-load-indicator",25),e.qZA())}function te(n){return void 0!==n?.roleName&&void 0!==n?.group}function ne(n){return void 0!==n?.customerAccounts&&void 0!==n?.districtIds&&void 0!==n?.email}const ie=[{path:"",component:(()=>{class n{constructor(t){this._http=t,this.isLoading=!0,this.diffClicked=i=>{this.currentChange=i.row.data,this.showDiff=!0},this.calculateId=i=>{try{return null===i.new?"User has been deleted":i.old?i.old?.name?i.old?.name:i.old?.email:i.new?.name?i.new?.name:i.new?.email}catch(r){console.error(r,i)}},this.calculateUserId=i=>{try{return i.user?.email?i.user?.email:i.user?.name}catch(r){console.error(r,i)}}}ngOnInit(){this.isLoading=!0,this._http.get(`${Q.N.api.url}/AuthHistory`).pipe((0,I.d)(),(0,N.K)(i=>(console.error("Failed to load auth history:",i),this.isLoading=!1,[]))).subscribe(i=>{this.dataSource=i.map(l=>({...l,type:te(l.new??l.old)?"Role":ne(l.new??l.old)?"UserProfile":"?"}));const r=new Date;r.setHours(0,0,0,0);const d=new Date;d.setHours(23,59,59,999);const m=[...this.dataSource.map(l=>new Date(l.createdAt)),r,d].sort((l,T)=>l<T?-1:l>T?1:0);this.rangeStart=m[0],this.rangeStart.setHours(0,0,0,0),this.rangeEnd=m[m.length-1],this.rangeEnd.setHours(23,59,59,999),this.valueStart=new Date,this.valueStart.setHours(0,0,0,0),this.valueEnd=new Date,this.valueEnd.setHours(23,59,59,999),this.isLoading=!1})}onRangeSelectorValueChanged(t){this.selected=this.dataSource.filter(i=>+new Date(i.createdAt)>=+t.value[0]&&+new Date(i.createdAt)<=+t.value[1])}onRangeSelectorInitialized(t){const i=t.component.option("value");this.selected=this.dataSource.filter(r=>+new Date(r.createdAt)>=+i[0]&&+new Date(r.createdAt)<=+i[1])}onToolbarPreparing(t){t.toolbarOptions.items.unshift({location:"after",widget:"dxButton",options:{hint:"Export",icon:"exportselected",onClick:()=>this.exportAsJSON(t)}},{location:"after",widget:"dxButton",options:{hint:"Reset Filters",icon:"fa fa-undo",onClick:()=>this.resetFilters()}})}resetFilters(){const t=this.historyGrid?.instance;t&&(t.clearFilter(),t.clearSorting(),t.refresh())}exportAsJSON(t){var i=this;return(0,S.Z)(function*(){let r=yield i.historyGrid.instance.getSelectedRowsData();r.length>0?(r=r.map(d=>({...d,summary:w.diff(d.old,d.new)})),(0,U.saveAs)(new Blob([JSON.stringify(r)],{type:"application/json"}),"auth-history.json")):(0,J.Z9)("No rows selected.  Select the changes you would like to export","No changes selected"),i.isLoading=!1,t.cancel=!0})()}}return n.\u0275fac=function(t){return new(t||n)(e.Y36(P.eN))},n.\u0275cmp=e.Xpm({type:n,selectors:[["app-auth-history"]],viewQuery:function(t,i){if(1&t&&e.Gf(G,5),2&t){let r;e.iGM(r=e.CRH())&&(i.historyGrid=r.first)}},decls:2,vars:2,consts:[[4,"ngIf"],["style","width: 100%; height: 100%;",4,"ngIf"],[1,"dx-card","content-block","responsive-paddings"],["id","range-selector",3,"value","onInitialized","onValueChanged",4,"ngIf"],[3,"dataSource","onToolbarPreparing"],["historyGrid",""],["mode","multiple"],["placeholder","Search...",3,"visible"],[3,"visible"],["caption","User Id","dataType","string",3,"allowSorting","allowSearch","calculateCellValue"],["dataField","createdAt","dataType","datetime"],["dataField","type","dataType","string"],["caption","Object Updated","dataType","string",3,"allowSorting","allowSearch","calculateCellValue"],["type","buttons",3,"width"],["icon","showpanel","hint","diff",3,"onClick"],["title","Changes",3,"visible","showCloseButton","hideOnOutsideClick","visibleChange"],[4,"dxTemplate","dxTemplateOf"],["id","range-selector",3,"value","onInitialized","onValueChanged"],[3,"startValue","endValue"],["format","monthAndDay"],[2,"width","100%","display","flex"],[2,"flex-grow","1","width","50%"],[2,"flex-grow","1"],[4,"ngFor","ngForOf"],[2,"width","100%","height","100%"],["id","large-indicator","height","300","width","300",1,"centered"]],template:function(t,i){1&t&&(e.YNc(0,k,16,16,"div",0),e.YNc(1,ee,2,0,"div",1)),2&t&&(e.Q6J("ngIf",!i.isLoading),e.xp6(1),e.Q6J("ngIf",i.isLoading))},dependencies:[c.sg,c.O5,M.p6,u.k4o,R.e,u.qvW,u.ecQ,u.XXE,u.Lz9,b.N,D.x,V.Y,z.A,u.yk0,u.RlF,c.Ts,c.uU,c.Nd,B],styles:["#range-select{height:400px}[_nghost-%COMP%]{width:100%}pre[_ngcontent-%COMP%]{overflow-x:auto;white-space:pre-wrap;white-space:-moz-pre-wrap;white-space:-pre-wrap;white-space:-o-pre-wrap;word-wrap:break-word}.centered[_ngcontent-%COMP%]{position:fixed;top:50%;left:50%;transform:translate(-50%,-50%)}"]}),n})(),data:{pageTitle:"Auth History"},canActivate:[O.RQ,H.Sg]}];let oe=(()=>{class n{}return n.\u0275fac=function(t){return new(t||n)},n.\u0275mod=e.oAB({type:n}),n.\u0275inj=e.cJS({imports:[y.Bz.forChild(ie),y.Bz]}),n})(),re=(()=>{class n{}return n.\u0275fac=function(t){return new(t||n)},n.\u0275mod=e.oAB({type:n}),n.\u0275inj=e.cJS({imports:[c.ez,oe,Z.m]}),n})()}}]);