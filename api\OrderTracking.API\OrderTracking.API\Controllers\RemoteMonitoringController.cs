using System;
using System.Threading.Tasks;
using ClientPortal.Shared.Extensions;
using ClientPortal.Shared.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using OrderTracking.API.Services;

namespace OrderTracking.API.Controllers
{
    /// <summary> 
    ///     API Controller for reading remote monitoring sensor readings from Smartpims
    /// </summary> 
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Policy = "UserIsActive")]
    public class RemoteMonitoringController : ControllerBase
    {
        private readonly ISensorReadingsSQLService _sensors;
        private readonly IUserProfilesService _userProfiles;
        private readonly ILogger<RemoteMonitoringController> _logger;

        /// <summary>
        ///     Constructor for controller
        /// </summary>
        /// <param name="sensors"></param>
        /// <param name="userProfiles"></param>
        /// <param name="logger"></param>
        public RemoteMonitoringController(ISensorReadingsSQLService sensors, IUserProfilesService userProfiles, ILogger<RemoteMonitoringController> logger)
        {
            _userProfiles = userProfiles;
            _sensors = sensors;
            _logger = logger;
        }

        /// <summary>
        ///     GET endpoint
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                var email = User?.Identity?.Name?.ToLower();
                _logger.LogInformation($"RemoteMonitoring request from user: {email}");

                var user = await _userProfiles.GetAsync(email);
                if (user == null)
                {
                    _logger.LogWarning($"User profile not found for: {email}");
                    return Unauthorized();
                }

                _logger.LogInformation($"User {email} has roles: {string.Join(", ", user.Roles ?? new List<string>())}");

                if (user.HasRole("app:admin"))
                {
                    _logger.LogInformation("Admin user - getting all sensor readings");
                    var adminReadings = await _sensors.GetSensorReadingsAsync();
                    return Ok(adminReadings);
                }
                else
                {
                    _logger.LogInformation("Regular user - getting filtered sensor readings");
                    var userReadings = await _sensors.GetSensorReadingsForUserAsync(user);
                    return Ok(userReadings);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in RemoteMonitoring endpoint: {ex.Message}");
                _logger.LogError($"Stack trace: {ex.StackTrace}");

                // Check if it's a SQL connection issue
                if (ex.Message.Contains("connection") || ex.Message.Contains("database") || ex.Message.Contains("login"))
                {
                    return StatusCode(500, new {
                        error = "Database connection failed",
                        message = "Remote monitoring database is not accessible",
                        details = ex.Message
                    });
                }

                return StatusCode(500, new {
                    error = "Internal server error",
                    message = ex.Message
                });
            }
        }
    }
}