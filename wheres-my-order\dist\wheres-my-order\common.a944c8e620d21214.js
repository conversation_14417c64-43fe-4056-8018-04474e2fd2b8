"use strict";(self.webpackChunkwheres_my_order=self.webpackChunkwheres_my_order||[]).push([[592],{31140:($,x,r)=>{r.d(x,{H:()=>c,f:()=>e});var u=r(16714),g=r(17290),i=r(54004),m=r(92340),v=r(98274),s=r(80529);let e=(()=>{class t{constructor(o){this._httpService=o}create(o){return this._httpService.post(`${m.N.api.url}/roles`,o,{observe:"response"}).pipe((0,i.U)(l=>{l.headers.get("location")}))}delete(o){return this._httpService.delete(`${m.N.api.url}/roles/${o}`)}update(o,l){return this._httpService.put(`${m.N.api.url}/roles/?originalId=${l}`,o)}getRoles(){return this._httpService.get(`${m.N.api.url}/roles`)}getUsersForRole(o){return this._httpService.get(`${m.N.api.url}/roles/${o}/users`)}getRoleKeys(){return this.getRoles().pipe((0,i.U)(o=>o.map(l=>l.id)))}getAllAsDataSource(){return this.getRoles().pipe((0,i.U)(o=>new g.Z({store:new u.Z({data:o,key:"id"}),sort:"id"})))}addUserToRole(o,l){return this._httpService.put(`${m.N.api.url}/roles/${o}/users/${l}`,null)}deleteUserFromRole(o,l){return this._httpService.delete(`${m.N.api.url}/roles/${o}/users/${l}`)}}return t.\u0275fac=function(o){return new(o||t)(v.LFG(s.eN))},t.\u0275prov=v.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),c=(()=>{class t{constructor(o){this._httpService=o}getCustomerAccounts(o){return this._httpService.get(`${m.N.api.url}/orders/customerAccounts`,{params:o})}}return t.\u0275fac=function(o){return new(o||t)(v.LFG(s.eN))},t.\u0275prov=v.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},98069:($,x,r)=>{r.r(x),r.d(x,{ProfileModule:()=>b});var u=r(36895),g=r(67837),i=r(98350),m=r(61978),v=r(80529),s=r(39646),e=r(54004),c=r(63900),t=r(98274),p=r(63326),o=r(31140),l=r(99106),O=r(68665),C=r(1181);let S=(()=>{class n{transform(a){return a&&a.idTokenClaims?a.idTokenClaims.emails[0]:null}}return n.\u0275fac=function(a){return new(a||n)},n.\u0275pipe=t.Yjl({name:"emailClaim",type:n,pure:!0}),n})();function P(n,d){if(1&n&&(t.TgZ(0,"div",15)(1,"span"),t._uU(2),t.qZA()()),2&n){const a=d.$implicit;t.xp6(2),t.AsE(" ",a.externalCustomer," - ",a.externalCustomerName," ")}}function A(n,d){if(1&n&&(t.TgZ(0,"dx-tag-box",13),t.YNc(1,P,3,2,"div",14),t.qZA()),2&n){const a=t.oxw().ngIf;t.Q6J("value",a)("readOnly",!0),t.xp6(1),t.Q6J("dxTemplateOf","readOnlyTemplate")}}function T(n,d){if(1&n&&(t.TgZ(0,"div",8)(1,"div",9),t._uU(2,"Client Accounts:"),t.qZA(),t.YNc(3,A,2,3,"dx-tag-box",12),t.qZA()),2&n){const a=d.ngIf;t.xp6(3),t.Q6J("ngIf",a.length>0)}}function N(n,d){if(1&n&&(t.TgZ(0,"div",15)(1,"span"),t._uU(2),t.qZA()()),2&n){const a=d.$implicit;t.xp6(2),t.Oqu(a)}}function Z(n,d){if(1&n&&(t.TgZ(0,"dx-tag-box",13),t.YNc(1,N,3,1,"div",14),t.qZA()),2&n){const a=t.oxw().ngIf;t.Q6J("value",a)("readOnly",!0),t.xp6(1),t.Q6J("dxTemplateOf","readOnlyTemplate")}}function M(n,d){if(1&n&&(t.TgZ(0,"div",8)(1,"div",9),t._uU(2,"District IDs:"),t.qZA(),t.YNc(3,Z,2,3,"dx-tag-box",12),t.qZA()),2&n){const a=d.ngIf;t.xp6(3),t.Q6J("ngIf",a.length>0)}}let U=(()=>{class n{constructor(a,h,f){this._auth=a,this._users=h,this._customerAccounts=f}ngOnInit(){this.user=this._auth.user,this.profile$=this._users.currentProfile$,this.getDistrictIds(),this.getCustomerAccounts()}queryParamsFromAccountNumbers(a){const h=a.map(f=>["externalCustomer","=",f.toString()]).map((f,y)=>y<a.length-1?[f,"or"]:[f]).reduce((f,y)=>f.concat(y),[]);return(new v.LE).set("filter",JSON.stringify(h))}getDistrictIds(){this.districtIds$=this.profile$.pipe((0,e.U)(a=>a.districtIds))}getCustomerAccounts(){this.customerAccounts$=this.profile$.pipe((0,e.U)(a=>a.customerAccounts),(0,c.w)(a=>{if(a?.length<=0)return(0,s.of)([]);const h=this.queryParamsFromAccountNumbers(a);return this._customerAccounts.getCustomerAccounts(h).pipe((0,e.U)(f=>f.data))}))}}return n.\u0275fac=function(a){return new(a||n)(t.Y36(p.e8),t.Y36(p.fz),t.Y36(o.H))},n.\u0275cmp=t.Xpm({type:n,selectors:[["ng-component"]],decls:22,vars:16,consts:[[1,"content-block","dx-card","responsive-paddings","user-card"],[1,"user-card-header"],[1,"user-card-content"],[1,"user-card-name"],[1,"user-card-email"],[1,"dx-icon-email"],["target","_blank",3,"href"],[1,"dx-fieldset"],[1,"dx-field"],[1,"dx-field-label"],[1,"dx-field-value"],["class","dx-field",4,"ngIf"],["class","dx-field-value","tagTemplate","readOnlyTemplate","placeholder","",3,"value","readOnly",4,"ngIf"],["tagTemplate","readOnlyTemplate","placeholder","",1,"dx-field-value",3,"value","readOnly"],["class","dx-tag-content","style","padding-right: 10px",4,"dxTemplate","dxTemplateOf"],[1,"dx-tag-content",2,"padding-right","10px"]],template:function(a,h){1&a&&(t.TgZ(0,"div",0),t._UZ(1,"div",1),t.TgZ(2,"div",2)(3,"span",3),t._uU(4),t.qZA(),t.TgZ(5,"span",4),t._UZ(6,"span",5),t.TgZ(7,"a",6),t.ALo(8,"emailClaim"),t._uU(9),t.ALo(10,"emailClaim"),t.qZA()(),t.TgZ(11,"div",7)(12,"div",8)(13,"div",9),t._uU(14,"Company:"),t.qZA(),t.TgZ(15,"div",10),t._uU(16),t.ALo(17,"userCompany"),t.qZA()(),t.YNc(18,T,4,1,"div",11),t.ALo(19,"async"),t.YNc(20,M,4,1,"div",11),t.ALo(21,"async"),t.qZA()()()),2&a&&(t.xp6(4),t.Oqu(h.user.name),t.xp6(3),t.MGl("href","mailto:",t.lcZ(8,6,h.user),"",t.LSH),t.xp6(2),t.Oqu(t.lcZ(10,8,h.user)),t.xp6(7),t.Oqu(t.lcZ(17,10,h.user)),t.xp6(2),t.Q6J("ngIf",t.lcZ(19,12,h.customerAccounts$)),t.xp6(2),t.Q6J("ngIf",t.lcZ(21,14,h.districtIds$)))},dependencies:[u.O5,l.p6,O.U,u.Ov,C.i,S],styles:[".form-avatar[_ngcontent-%COMP%]{float:left;height:120px;width:120px;margin-right:20px;border:1px solid rgba(0,0,0,.1);background-size:contain;background-repeat:no-repeat;background-position:center;background-color:#fff;overflow:hidden}.form-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:120px;display:block;margin:0 auto}[_nghost-%COMP%]{width:100%}[_nghost-%COMP%]   .dx-fieldset[_ngcontent-%COMP%]{width:100%}.user-card[_ngcontent-%COMP%] > .user-card-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-start}.user-card[_ngcontent-%COMP%] > .user-card-content[_ngcontent-%COMP%]   .user-card-name[_ngcontent-%COMP%]{font-size:2em}.user-card[_ngcontent-%COMP%] > .user-card-content[_ngcontent-%COMP%]   .user-card-phone[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .user-card[_ngcontent-%COMP%] > .user-card-content[_ngcontent-%COMP%]   .user-card-email[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{margin-right:.7em}"],changeDetection:0}),n})();r(21596);const I=[{path:"user",data:{pageTitle:"User Profile"},component:U,canActivate:[m.RQ]}];let R=(()=>{class n{}return n.\u0275fac=function(a){return new(a||n)},n.\u0275mod=t.oAB({type:n}),n.\u0275inj=t.cJS({imports:[i.Bz.forChild(I),i.Bz]}),n})(),b=(()=>{class n{}return n.\u0275fac=function(a){return new(a||n)},n.\u0275mod=t.oAB({type:n}),n.\u0275inj=t.cJS({imports:[u.ez,g.m,R]}),n})()},46611:($,x,r)=>{r.d(x,{gi:()=>i,iT:()=>m});var u=r(83582);const g="->";class i{constructor(s){if(s)for(const[e,c]of Object.entries(s))this[e]="dateTimeUTC"===e?new Date(c):c}static keyMatches(s,e){const c=s.split(g),t=s.includes(e.plant)||!e.plant&&"(blank)"===c[0],p=s.includes(e.asset)||!e.asset&&"(blank)"===c[1],o=s.includes(e.collectionPoint)||!e.collectionPoint&&"(blank)"===c[2],l=s.includes(e.tml)||!e.tml&&"(blank)"===c[3];return t&&p&&o&&l}static getPlantKeys(s){return[...new Set(s.filter(e=>(0,u.UE)(e)&&(i.hasNSeparators(e,0)||i.hasNSeparators(e,1)||i.hasNSeparators(e,2)||i.hasNSeparators(e,3))).map(e=>e.split(g)[0])).values()]}static getAssetKeys(s){return[...new Set(s.filter(e=>(0,u.UE)(e)&&(i.hasNSeparators(e,1)||i.hasNSeparators(e,2)||i.hasNSeparators(e,3))).map(e=>e.split(g)[1]))]}static getCollectionPointKeys(s){return[...new Set(s.filter(e=>(0,u.UE)(e)&&(i.hasNSeparators(e,2)||i.hasNSeparators(e,3))).map(e=>e.split(g)[2]))]}static getTMLKeys(s){const e=s.filter(p=>(0,u.UE)(p)&&i.hasNSeparators(p,3)).map(p=>p.split(g)[3]);return[...new Set(e)]}static hasNSeparators(s,e){return(s.match(/->/g)||[])?.length===e}}class m{constructor(s,e,c){this.id=s,this.text=(0,u.UE)(e)?e:"(blank)",this.parentId=c}static plantId(s){return this.markBlank(s.plant)}static assetId(s){return`${this.markBlank(s.plant)}->${this.markBlank(s.asset)}`}static collectionPointId(s){return`${this.markBlank(s.plant)}->${this.markBlank(s.asset)}->${this.markBlank(s.collectionPoint)}`}static tmlId(s){return`${this.markBlank(s.plant)}->${this.markBlank(s.asset)}->${this.markBlank(s.collectionPoint)}->${this.markBlank(s.tml)}`}static markBlank(s){return(0,u.UE)(s)?s:"(blank)"}}},64149:($,x,r)=>{r.d(x,{n:()=>o});var u=r(61135),g=r(39841),i=r(54004),m=r(34782),v=r(92340),s=r(12739),e=r(46611),c=r(98274),t=r(80529),p=r(63326);let o=(()=>{class l{constructor(C,S){this._http=C,this._users=S,this._readings$=this._http.get(`${v.N.api.url}/RemoteMonitoring`).pipe((0,i.U)(P=>P.map(A=>new e.gi(A))),(0,m.d)()),this._demoReadings$=this._http.get("assets/data/sensor-readings.json").pipe((0,i.U)(P=>P.map(A=>new e.gi(A))),(0,m.d)()),this.readingsSubject=new u.X([]),this.selectedReadingsSubject=new u.X([]),this._demoReadings=[],this._readings=[],this.readings$=this.readingsSubject.asObservable(),this.selectedReadings$=this.selectedReadingsSubject.asObservable(),(0,g.a)([this._users.currentProfile$.pipe((0,s.p2)("RemoteMonitoring:Demo")),this._readings$,this._demoReadings$]).subscribe(([P,A,T])=>{this._readings=A,this._demoReadings=T,this.readingsSubject.next(P?[...this._readings,...this._demoReadings]:this._readings)})}selectProject(C){"demo"===C.toLowerCase()?this.selectedReadingsSubject.next(this._demoReadings):this.selectedReadingsSubject.next(this._readings.filter(S=>S.site===C))}}return l.\u0275fac=function(C){return new(C||l)(c.LFG(t.eN),c.LFG(p.fz))},l.\u0275prov=c.Yz7({token:l,factory:l.\u0275fac,providedIn:"root"}),l})()}}]);