using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using ClientPortal.Shared.Models.RemoteMonitoring;
using Dapper;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Z.Dapper.Plus;

namespace ClientPortal.Shared.Services
{
    /// <summary>
    ///     Implementation of service class for making SQL operations on SensorReadings
    /// </summary>
    public class SensorReadingsSQLService : ISensorReadingsSQLService
    {
        private readonly IConfiguration _config;
        private readonly ILogger<SensorReadingsSQLService> _logger;

        public SensorReadingsSQLService(ILogger<SensorReadingsSQLService> logger, IConfiguration config)
        {
            _logger = logger;
            _config = config;

            // Make sure we are allowed to use Dapper Plus for the bulk insert
            var licenseName = config["ZDapperPlus:LicenseName"];
            var licenseKey = config["ZDapperPlus:LicenseKey"];
            DapperPlusManager.AddLicense(licenseName, licenseKey);
        }

        public void MergeSensorReadings(IEnumerable<SensorReading> sensorReadings)
        {
            try
            {
                var connectionString = _config.GetConnectionString("RemoteMonitoring");

                // Use a connection to sql server
                using var connection = new SqlConnection(connectionString);

                _logger.LogInformation(
                    "bulk inserting sensor data, inserting only if the record doesn't already exist.");

                connection
                    // Want to make sure that we only insert what we don't already have
                    .UseBulkOptions(options => options.InsertIfNotExists = true)
                    // Attempt to insert all sensor readings
                    .BulkInsert(sensorReadings);
            }
            catch (Exception e)
            {
                _logger.LogError(e.Message);
                throw;
            }
        }

        public async Task<IEnumerable<SensorReading>> GetSensorReadingsAsync()
        {
            try
            {
                var connectionString = _config.GetConnectionString("RemoteMonitoring");
                _logger.LogInformation($"Using connection string: {connectionString?.Substring(0, Math.Min(50, connectionString.Length ?? 0))}...");

                if (string.IsNullOrEmpty(connectionString))
                {
                    _logger.LogError("RemoteMonitoring connection string is null or empty");
                    throw new InvalidOperationException("RemoteMonitoring connection string is not configured");
                }

                // Check for placeholder values
                if (connectionString.Contains("{your_username}") || connectionString.Contains("{your_password}"))
                {
                    _logger.LogError("Connection string contains placeholder values: {your_username} or {your_password}");
                    throw new InvalidOperationException("Connection string has not been properly configured - contains placeholder values");
                }

                // Use a connection to sql server
                await using var connection = new SqlConnection(connectionString);

                _logger.LogInformation("Attempting to open SQL connection for sensor readings");
                await connection.OpenAsync();
                _logger.LogInformation("SQL connection opened successfully");

                var readings = await connection.QueryAsync<SensorReading>("SELECT * FROM dbo.SensorReadings");
                var orderedReadings = readings.Select(r =>
                {
                    // Make sure that we mark these as UTC coming out of the database so that they get serialized
                    // and sent to the front end as such.
                    r.DateTimeUTC = DateTime.SpecifyKind(r.DateTimeUTC, DateTimeKind.Utc);
                    return r;
                }).OrderBy(r => r.DateTimeUTC);

                _logger.LogInformation($"Retrieved {readings.Count()} sensor readings");
                return orderedReadings;
            }
            catch (Exception e)
            {
                _logger.LogError(e.Message);
                throw;
            }
        }

        public async Task<IEnumerable<SensorReading>> GetSensorReadingsForUserAsync(UserProfile user)
        {
            try
            {
                var connectionString = _config.GetConnectionString("RemoteMonitoring");
                _logger.LogInformation($"Getting sensor readings for user: {user.Id}");

                if (string.IsNullOrEmpty(connectionString))
                {
                    _logger.LogError("RemoteMonitoring connection string is null or empty");
                    throw new InvalidOperationException("RemoteMonitoring connection string is not configured");
                }

                // Check for placeholder values
                if (connectionString.Contains("{your_username}") || connectionString.Contains("{your_password}"))
                {
                    _logger.LogError("Connection string contains placeholder values");
                    throw new InvalidOperationException("Connection string has not been properly configured - contains placeholder values");
                }

                // Use a connection to sql server
                await using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                _logger.LogInformation($"User has {user.RemoteMonitoringSiteIds?.Count ?? 0} remote monitoring site IDs");

                // if the total length of the query string ever exceeds 4096 characters we will run into an error and have to update this method
                var sites ="'"+String.Join("','", user.RemoteMonitoringSiteIds ?? new List<string>())+"'";
                _logger.LogInformation($"Querying for sites: {sites}");

                var readings = await connection.QueryAsync<SensorReading>($"SELECT * FROM dbo.SensorReadings WHERE Site IN ({sites}) ");
                var orderedReadings = readings.Select(r =>
                {
                    // Make sure that we mark these as UTC coming out of the database so that they get serialized
                    // and sent to the front end as such.
                    r.DateTimeUTC = DateTime.SpecifyKind(r.DateTimeUTC, DateTimeKind.Utc);
                    return r;
                }).OrderBy(r => r.DateTimeUTC);

                _logger.LogInformation($"Retrieved {readings.Count()} sensor readings for user");
                return orderedReadings;
            }
            catch (Exception e)
            {
                _logger.LogError($"Error getting sensor readings for user: {e.Message}");
                throw;
            }
        }
    }
}