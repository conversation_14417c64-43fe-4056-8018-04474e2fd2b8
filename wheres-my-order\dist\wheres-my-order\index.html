<!DOCTYPE html>
<html>

<head>
    <title>TEAM Digital</title>
    <base href="/"/>

    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>

    <link rel="icon" type="image/x-icon" href="favicon.ico"/>

    <style>
        body.main-body {
            width: 100vw;
            height: 100vh;
        }

        body.main-body .splash-screen {
            width: 100vw;
            height: 100vh;
        }

        body.main-body .splash-screen .splash-logo {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    </style>
<link rel="stylesheet" href="styles.fb961a60d1bd964b.css"></head>

<body class="main-body dx-viewport">
    <app-root>
        <div class="splash-screen">
            <img class="splash-logo" src="assets/images/TEAM.Mark_CMYK.SVG" width="275"/>
        </div>
    </app-root>
    <app-redirect></app-redirect>
<script src="runtime.d8b8a142a3a86abd.js" type="module"></script><script src="polyfills.46953175bf77da80.js" type="module"></script><script src="main.836ae36949954f91.js" type="module"></script></body>

<script>
    const isIE = window.navigator.userAgent.indexOf('MSIE ') > -1 ||
        window.navigator.userAgent.indexOf('Trident/') > -1;

    if (isIE) {
        alert('Unsupported browser detected. Please change to a supported browser such as: Chrome, Firefox, or Edge.')
    }
</script>

</html>
