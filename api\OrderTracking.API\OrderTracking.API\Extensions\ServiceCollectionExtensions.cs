using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
//using AIMaaS.Models;
//using AIMaaS.Services;
using APMWebDataInterface.ExampleDataModel;
using APMWebDataInterface.Headless;
using Audit.Core;
using Audit.SqlServer;
using Audit.SqlServer.Providers;
using ClientPortal.Shared.Models;
using ClientPortal.Shared.Models.MOS;
using ClientPortal.Shared.Services;
// Removed Firebase and Google Cloud imports - migrated to Azure
// using FirebaseAdmin;
// using Google.Apis.Auth.OAuth2;
// using Google.Cloud.Firestore;
using Microsoft.Azure.Cosmos;
using Microsoft.CodeAnalysis;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using OrderTracking.API.Interfaces;
using OrderTracking.API.Interfaces.Services;
using OrderTracking.API.Models;
using OrderTracking.API.Models.PowerBI;
using OrderTracking.API.Repositories;
using OrderTracking.API.Services;
using OrderTracking.API.Services.APM;
using OrderTracking.API.Services.PowerBI;
using Swashbuckle.AspNetCore.SwaggerGen;
//using TeamDigital.PipelineInspection.APIManager;
using Z.Dapper.Plus;

namespace OrderTracking.API.Extensions
{
    /// <summary>
    ///     Extension methods for ServiceCollection to simplify a web api's Startup class.
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        private static IServiceCollection InitializeCosmosClientInstanceAsync(this IServiceCollection services,
            IConfiguration configuration)
        {
            // Support both connection string and endpoint+key approaches
            var connectionString = configuration["Connections:ConnectionString"];
            var account = configuration["Connections:Endpoint"];
            var key = configuration["Connections:AuthKey"];

            // Usage
            var databaseId = configuration["Connections:DatabaseName"];

            // Migrated from Firestore to Azure Cosmos DB
            // var client = new ValidatingFirestoreClient(configuration["Connections:ProjectId"], databaseId);
            // var adapter = new FirestoreClientAdapter(client);
            // services.AddSingleton<IFirestoreClientAdapter>(adapter);

            ValidatingCosmosClient clientCosmo;

            // Create ValidatingCosmosClient instance using connection string or endpoint+key
            var cosmosOptions = new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Gateway,
                RequestTimeout = TimeSpan.FromSeconds(120),
                MaxRetryAttemptsOnRateLimitedRequests = 5,
                MaxRetryWaitTimeOnRateLimitedRequests = TimeSpan.FromSeconds(60)
            };

            if (!string.IsNullOrEmpty(connectionString))
            {
                // Use connection string approach
                clientCosmo = new ValidatingCosmosClient(connectionString, cosmosOptions);
            }
            else
            {
                // Use endpoint + key approach (original)
                clientCosmo = new ValidatingCosmosClient(account, key, cosmosOptions);
            }

            // Register both CosmosClient and ValidatingCosmosClient for dependency injection
            services.AddSingleton<CosmosClient>(clientCosmo);
            services.AddSingleton<ValidatingCosmosClient>(clientCosmo);

            // Register the adapter
            var adapterCosmo = new CosmosClientAdapter(clientCosmo);
            services.AddSingleton<ICosmosClientAdapter>(adapterCosmo);

            return services;
        }

        /// <summary>
        ///     Registers services and configuration options related to send grid so that the API can
        ///     send emails programmatically.
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        private static IServiceCollection AddSendGrid(this IServiceCollection services, IConfiguration configuration)
        {
            if (configuration == null) throw new ArgumentNullException(nameof(configuration));

            var sendGridConfig = configuration.GetSection(nameof(ClientPortal.Shared.Models.SendGrid));
            services.Configure<ClientPortal.Shared.Models.SendGrid>(sendGridConfig);
            services.AddScoped<IEmailService, EmailService>();

            return services;
        }

        /// <summary>
        ///     Add services related to authorization (users, roles, etc.) for the client portal.
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        private static IServiceCollection AddPortalAuthorizationServices(this IServiceCollection services,
            IConfiguration configuration)
        {
            if (configuration == null) throw new ArgumentNullException(nameof(configuration));
            var config = configuration.GetSection("Connections");

            // Register ContainerFactory as a service instead of creating it immediately
            // This defers the ValidatingCosmosClient initialization until it's actually needed
            services.AddSingleton<IContainerFactory>(serviceProvider =>
            {
                var cosmosClient = serviceProvider.GetRequiredService<ValidatingCosmosClient>();
                return new ContainerFactory(config, cosmosClient);
            });

            // Register services using factory pattern to avoid premature service provider build
            services.AddScoped<IRolesService>(serviceProvider =>
            {
                var containerFactory = serviceProvider.GetRequiredService<IContainerFactory>();
                return new RolesCosmosService(containerFactory, serviceProvider, configuration);
            });

            services.AddScoped<IUserProfilesService>(serviceProvider =>
            {
                var containerFactory = serviceProvider.GetRequiredService<IContainerFactory>();
                return new UserProfilesCosmosService(containerFactory, serviceProvider, configuration);
            });

            services.AddScoped<IUserAgreementService, UserAgreementService>();
            
            // Add HttpContextAccessor for UserProfilesCosmosService
            services.AddHttpContextAccessor();
            
            // Register AuthHistoryService - migrated to Cosmos DB
            services.AddScoped<IAuthHistoryService, AuthHistoryCosmosService>();
            
            return services;
        }

        ///// <summary>
        /////     Add WMO services to perform CRUD operations on Orders
        ///// </summary>
        ///// <param name="services"></param>
        ///// <param name="configuration"></param>
        ///// <returns></returns>
        //private static IServiceCollection AddWhereMyOrderServices(this IServiceCollection services,
        //    IConfiguration configuration)
        //{
        //    //services.AddScoped<IOrdersService, OrdersService>();
        //    services.AddDbContext<OrderContext>(options =>
        //        options.UseSqlServer(configuration.GetSection("SQLConnections")["Orders"]));
        //    services.AddScoped<IClientPortalResultsLoader, ClientPortalResultsLoader>();
        //    services.AddScoped<IOrdersJobsService, OrdersJobsService>();

        //    DapperPlusManager.AddLicense(configuration.GetValue<string>("ZDapperPlus:LicenseName"),
        //        configuration.GetValue<string>("ZDapperPlus:LicenseKey"));

        //    return services;
        //}

        ///// <summary>
        /////     Add EDR service to perform CRUD operations on Equipment Demand Requests
        ///// </summary>
        ///// <param name="services"></param>
        ///// <param name="configuration"></param>
        ///// <returns></returns>
        //private static IServiceCollection AddEquipmentDemandRequestServices(this IServiceCollection services,
        //    IConfiguration configuration)
        //{
        //    if (configuration == null) throw new ArgumentNullException(nameof(configuration));

        //    services.AddSingleton<IEquipmentRequestsService, EquipmentRequestsService>();

        //    return services;
        //}

        /// <summary>
        ///     Adds Azure Blob Storage and creates singleton services for various resources.
        ///     Each resource gets its own singleton service to upload file blobs and associate
        ///     the file's meta data to the resource in question.
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        private static IServiceCollection AddAzureStorageService(this IServiceCollection services,
            IConfiguration configuration)
        {
            if (configuration == null) throw new ArgumentNullException(nameof(configuration));

            var configurationSection = configuration.GetSection(nameof(BlobStorage));
            services.Configure<BlobStorage>(configurationSection);
            //services.AddSingleton<IWMOBlobStorageService, WMOBlobStorageService>();
            //services.AddSingleton<IEDRBlobStorageService, EDRBlobStorageService>();
            services.AddSingleton<IAPMBlobStorageService, APMBlobStorageService>();
            //services.AddSingleton<IAPMReportingBlobStorageService, APMReportingBlobStorageService>();

            return services;
        }

        ///// <summary>
        /////     Register injectables for CredoSoft functionality
        ///// </summary>
        ///// <param name="services"></param>
        ///// <param name="configuration"></param>
        ///// <returns></returns>
        //private static IServiceCollection AddCredoSoftServices(this IServiceCollection services,
        //    IConfiguration configuration)
        //{
        //    if (configuration == null) throw new ArgumentNullException(nameof(configuration));

        //    services.Configure<CredoSoftData>(configuration.GetSection(nameof(CredoSoftData)));

        //    services.AddScoped<ICredoSoftService, CredoSoftService>();

        //    return services;
        //}

        private static IServiceCollection AddFieldServiceManagementServices(this IServiceCollection services,
            IConfiguration configuration)
        {
            services.Configure<FSMConfig>(configuration.GetSection(nameof(FSMConfig)));
            services.AddSingleton<IFieldServiceManagementService, FieldServiceManagementService>();
            return services;
        }

        private static IServiceCollection AddMechanicalAndOnStreamServices(this IServiceCollection services,
            IConfiguration configuration)
        {
            services.AddDbContext<MOSContext>();
            return services;
        }

        private static IServiceCollection AddAPMServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Migrated from Firebase to Azure - APM services now use Azure Cosmos DB
            // var credentials = configuration.GetValue<string>("APM:Firebase");
            // FirebaseApp.Create(new AppOptions
            // {
            //     Credential = GoogleCredential.FromJson(credentials)
            // });

            var driver = APM_WebDataInterface.Global;
            var environment = configuration.GetValue<string>("APM:Environment") == "production"
                ? APM_WebDataInterface.Databases.production
                : APM_WebDataInterface.Databases.testing;

            var cmsConfig = configuration.GetSection("APM:CmsSettings").Get<Dictionary<string, string>>();
            CMSSettings.Initialize(cmsConfig);
            driver.Initialize(environment).Wait();
            services.TryAddSingleton(driver);
            services
                .AddScoped<IJasperSoftService, JasperSoftService>()
                .AddScoped<ILeakReportService, LeakReportService>()
                .AddScoped<IProjectService, ProjectService>()
                .AddScoped<IAssetService, AssetService>()
                .AddScoped<IWorkOrderService, WorkOrderService>()
                .AddScoped<ILocationService, LocationService>()
                .AddScoped<ITasksService, TasksService>();
            return services;
        }

        //private static IServiceCollection AddCRDServices(this IServiceCollection services, IConfiguration configuration)
        //{
        //    Configuration.DataProvider = new SqlDataProvider
        //    {
        //        ConnectionString = configuration.GetConnectionString("CRDDatabase"),
        //        Schema = "dbo",
        //        TableName = "AuditLog",
        //        IdColumnName = "EventId",
        //        JsonColumnName = "JsonData",
        //        LastUpdatedDateColumnName = "LastUpdatedDate",
        //        CustomColumns = new List<CustomColumn>
        //        {
        //            new("EventType", ev => ev.EventType),
        //            new("User", ev => ev.CustomFields["User"]),
        //            new("RecordId", ev => ev.CustomFields["RecordId"])
        //        }
        //    };
        //    services.AddScoped<ICRDService, CRDService>();
        //    return services;
        //}

        /// <summary>
        ///     Add services for Power BI Integration
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        private static IServiceCollection AddPowerBIServices(this IServiceCollection services,
            IConfiguration configuration)
        {
            services.AddScoped(typeof(AadService))
                .AddScoped(typeof(PbiEmbedService));
            services.Configure<AzureAd>(configuration.GetSection("AzureAd"))
                .Configure<PowerBI>(configuration.GetSection("PowerBI"));

            return services;
        }

        /// <summary>
        ///     Core method for adding Services for the Portal Application
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        public static IServiceCollection AddPortalApplicationServices(this IServiceCollection services,
            IConfiguration configuration)
        {
            if (configuration == null) throw new ArgumentNullException(nameof(configuration));

            // Binding to CosmosDB Connection settings to give us injectable IOptions<Connections>
            var configurationSection = configuration.GetSection(nameof(Connections));
            services.Configure<Connections>(configurationSection);

            // Add additional module or overall service configuration here.
            // This allows us to keep a single call in Startup to this method
            services.InitializeCosmosClientInstanceAsync(configuration)
                .AddScoped<IReleaseNotesService, ReleaseNotesService>()
                .AddScoped<IAuthHistoryService, AuthHistoryCosmosService>()
                .AddPortalAuthorizationServices(configuration)
                //.AddWhereMyOrderServices(configuration)
                //.AddEquipmentDemandRequestServices(configuration)
                .AddAzureStorageService(configuration)
                //.AddCredoSoftServices(configuration)
                //.AddPipelineInspectionServices(configuration)
                .AddFlangeCalculationServices()
                .AddRemoteMonitoringServices()
                //.AddFieldServiceManagementServices(configuration) // Temporarily disabled due to Dataverse connection issues
                .AddMechanicalAndOnStreamServices(configuration)
                .AddAPMServices(configuration)
                //.AddCRDServices(configuration)
                .AddPowerBIServices(configuration);


            // Emails (SendGrid)
            services.AddSendGrid(configuration);

            // Notifications
            services.AddSingleton<INotificationsService, NotificationsService>();

            // ZDapperPlus
            services.Configure<ZDapperPlus>(configuration.GetSection(nameof(ZDapperPlus)));
            services.Configure<Emails>(configuration.GetSection(nameof(Emails)));
            var provider = services.BuildServiceProvider();
            var zDapperPlus = (IOptions<ZDapperPlus>)provider.GetService(typeof(IOptions<ZDapperPlus>));
            DapperPlusManager.AddLicense(zDapperPlus.Value.LicenseName, zDapperPlus.Value.LicenseKey);

            // DeploymentEnvironment
            services.AddScoped(typeof(DeploymentEnvironment));

            services.AddHttpClient();

            return services;
        }

        /// <summary>
        ///     Add services that provide the ability to get Pipeline Inspection data
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        //public static IServiceCollection AddPipelineInspectionServices(this IServiceCollection services,
        //    IConfiguration configuration)
        //{
        //    if (configuration == null) throw new ArgumentNullException(nameof(configuration));

        //    var piaWebAPIManager = new WebAPIManager();

        //    piaWebAPIManager.Initialize(configuration.GetConnectionString("PipelineInspection"));
        //    services.TryAddSingleton(piaWebAPIManager);


        //    return services;
        //}

        /// <summary>
        ///     Add services that provide the ability to get Flange Calculation data
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection AddFlangeCalculationServices(this IServiceCollection services)
        {
            services.AddScoped<ICalculationService, CalculationService>();

            return services;
        }

        /// <summary>
        ///     Add services that provide the ability to get sensor readings
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection AddRemoteMonitoringServices(this IServiceCollection services)
        {
            services.AddScoped<ISensorReadingsSQLService, SensorReadingsSQLService>();
            return services;
        }

        /// <summary>
        ///     Register the Swagger generator, defining 1 or more Swagger documents
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection AddSwaggerDocumentation(this IServiceCollection services)
        {
            services.AddSwaggerGen(c =>
            {
                c.CustomSchemaIds(type => type.ToString());

                var scheme = new OpenApiSecurityScheme
                {
                    Type = SecuritySchemeType.Http,
                    BearerFormat = "JWT",
                    Scheme = "bearer",
                    In = ParameterLocation.Header
                };
                c.AddSecurityDefinition("bearer", scheme);
                c.OperationFilter<AddAuthHeaderOperationFilter>();

                c.SwaggerDoc("v1", new OpenApiInfo
                {
                    Version = "v1",
                    Title = "Client Portal API",
                    Description = "API facilitating WMO and EDR feature modules"
                });

                // Set the comments path for the Swagger JSON and UI.

                // OrderTracking.API xml comments:
                var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                c.IncludeXmlComments(xmlPath);
            });

            services.AddSwaggerGenNewtonsoftSupport(); // explicit opt-in - needs to be placed after AddSwaggerGen()

            return services;
        }
    }

    public class CosmosClientAdapter : ICosmosClientAdapter
    {
        private readonly ValidatingCosmosClient _client;

        public CosmosClientAdapter(ValidatingCosmosClient client)
        {
            _client = client;
        }

        public ValidatingCosmosClient GetClient() => _client;

        public Container GetContainer(string databaseName, string containerName) =>
            _client.GetContainer(databaseName, containerName);
    }

    public interface ICosmosClientAdapter
    {
        ValidatingCosmosClient GetClient();
        Container GetContainer(string databaseName, string containerName);
    }

    // DEPRECATED: Firebase adapter classes - replaced by Azure Cosmos DB implementations
    // Commented out during Firebase-to-Azure migration cleanup
    /*
    public class FirestoreClientAdapter : IFirestoreClientAdapter
    {
        private readonly ValidatingFirestoreClient _client;

        public FirestoreClientAdapter(ValidatingFirestoreClient client)
        {
            _client = client;
        }

        public ValidatingFirestoreClient GetClient() => _client;

        // TODO: Replace with Azure Cosmos DB container references
        // public CollectionReference GetCollection(string collectionName) =>
        //     _client.GetCollection(collectionName);
        public object GetCollection(string collectionName) =>
            throw new NotImplementedException("Replace with Azure Cosmos DB container");

        // public CollectionReference GetCollection(string collectionName, out string partitionKeyPath) =>
        //     _client.GetCollection(collectionName, out partitionKeyPath);
        public object GetCollection(string collectionName, out string partitionKeyPath)
        {
            partitionKeyPath = string.Empty;
            throw new NotImplementedException("Replace with Azure Cosmos DB container");
        }
    }

    public interface IFirestoreClientAdapter
    {
        ValidatingFirestoreClient GetClient();
        // TODO: Replace with Azure Cosmos DB container references
        // CollectionReference GetCollection(string collectionName);
        // CollectionReference GetCollection(string collectionName, out string partitionKeyPath);
        object GetCollection(string collectionName);
        object GetCollection(string collectionName, out string partitionKeyPath);
    }
    */

    /// <summary>
    ///     Swashbuckle operation filter required to get bearer token authentication to work for api documentation pages
    /// </summary>
    public class AddAuthHeaderOperationFilter : IOperationFilter
    {
        /// <summary>
        ///     Applies the operation filter
        /// </summary>
        /// <param name="operation"></param>
        /// <param name="context"></param>
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            if (operation == null) throw new ArgumentNullException(nameof(operation));

            operation.Security ??= new List<OpenApiSecurityRequirement>();

            var scheme = new OpenApiSecurityScheme
            { Reference = new OpenApiReference { Type = ReferenceType.SecurityScheme, Id = "bearer" } };
            operation.Security.Add(new OpenApiSecurityRequirement { [scheme] = new List<string>() });
        }
    }
}