import moment from 'moment';
import { AssetCategoryAPICode } from '..';
import { isNullOrUndefined } from '../../../shared/helpers';
import {
    InspectionStatusBreakdown,
    ProjectVm,
    StatusesByBin,
    TaskVM,
    WorkTypePercentages
} from '../view-models';
import {
    CosmosAsset,
    CosmosAttribute,
    CosmosProject,
    CosmosTask,
    CosmosWorkOrder,
    ListChangeLog,
    ProjectActivityItem
} from './models';

export function getCurrentEntries(listChangeLog: ListChangeLog) {
    const sorted = Object.values(listChangeLog).sort((a, b) => a.T - b.T);
    let allEntries = Array.from(new Set(sorted.map((x) => x.V)));
    const allCopy = [...allEntries];
    for (const entry of allCopy) {
        const lastMatch = sorted
            .filter((x) => x.V === entry)
            .reduce(
                (prev, curr) =>
                    prev === null || curr.T >= prev.T ? curr : prev,
                null
            );
        if (lastMatch.A === 'Removed')
            allEntries = allEntries.filter((x) => x !== entry);
    }

    allEntries = allEntries.filter((x) => !isNullOrUndefined(x));

    return allEntries;
}

export function getCurrentValue<T>(attribute: CosmosAttribute<T>): T {
    if (!attribute || !attribute.ValueChangeLog) return attribute?.Value;

    const sorted = Object.values(attribute.ValueChangeLog).sort(
        (a, b) => a.T - b.T
    );
    const latest = sorted[sorted.length - 1];
    return latest ? latest.V : attribute.Value;
}

// Legacy Firebase conversion functions have been removed
// All code should now use Cosmos types directly

// Migrated from Firebase to Azure Cosmos DB
export function calculateAttributeValue<T>(attribute: CosmosAttribute<T>) {
    if (!attribute) return undefined;
    
    // Use direct Value if available
    if (attribute.Value !== undefined) {
        return attribute.Value;
    }
    
    // Otherwise try to get from ValueChangeLog
    if (attribute.ValueChangeLog && Object.keys(attribute.ValueChangeLog).length > 0) {
        const changeEntries = Object.values(attribute.ValueChangeLog);
        // Sort by timestamp and get the latest
        const sortedEntries = changeEntries.sort((a, b) => 
            (a as any).T > (b as any).T ? -1 : 1
        );
        return (sortedEntries[0] as any).V;
    }
    
    return undefined;
}

// Simplified Aggregation namespace for Azure compatibility
export namespace Aggregation {
    export function getActivitySummary(
        projectIds: string[],
        projects: any[],
        tasks: any[]
    ): any {
        // Simplified implementation for Azure migration
        return {
            projects: projects.filter(p => projectIds.includes(p.id)),
            tasks: tasks.filter(t => projectIds.includes(t.ProjectId))
        };
    }

    export function getAssetCategoriesSummary(assets: any[], projectIds: string[]) {
        // Simplified implementation for Azure migration
        return assets.filter(a => projectIds.some(pid => a.projectIds?.includes(pid)));
    }

    export function getStatusesByMonth(projectIds: string[], tasks: any[], assets: any[]) {
        // Fixed signature to match expected parameters
        return {};
    }

    export function getStatusesByWeek(projectIds: string[], tasks: any[], assets: any[]) {
        // Fixed signature to match expected parameters
        return {};
    }

    export function getWorkTypePercentages(projectActivities: any[], taskActivities: any[]) {
        // Simplified implementation for Azure migration
        return {};
    }

    export function getSummaryInfo(projects: any[], projectIds: string[], tasks: any[], workOrders: any[]) {
        // Simplified implementation for Azure migration
        return {
            totalProjects: projects.filter(p => projectIds.includes(p.id)).length,
            totalTasks: tasks.filter(t => projectIds.includes(t.ProjectId)).length,
            totalWorkOrders: workOrders.filter(w => projectIds.includes(w.ProjectId)).length
        };
    }

    export function getInspectionStatuses(
        projectIds: string[],
        tasks: any[],
        assets: any[],
        workOrders: any[]
    ) {
        // Simplified implementation for Azure migration
        return {
            completed: 0,
            inProgress: 0,
            notStarted: 0
        };
    }

    export function getTimelineTasksAndProjects(
        projectIds: string[],
        tasks: any[],
        projects: any[],
        workOrders: any[]
    ) {
        // Simplified implementation for Azure migration
        return {
            tasks: tasks.filter(t => projectIds.includes(t.ProjectId)),
            projects: projects.filter(p => projectIds.includes(p.id))
        };
    }

    export function getAssetsByAreaAndType(projectIds: string[], assets: any[]) {
        // Simplified implementation for Azure migration
        return assets.filter(a => projectIds.some(pid => a.projectIds?.includes(pid)));
    }

    export function createInspectionsWithoutDueDates(
        projectIds: string[],
        tasks: any[],
        assets: any[]
    ) {
        // Simplified implementation for Azure migration
        return tasks.filter(t =>
            projectIds.includes(t.ProjectId) &&
            (!t.dueDate || t.dueDate === null)
        );
    }
}
