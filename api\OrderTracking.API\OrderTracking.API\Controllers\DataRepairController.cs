using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Azure.Cosmos;
using OrderTracking.API.Repositories;
using ClientPortal.Shared.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace OrderTracking.API.Controllers
{
    /// <summary>
    /// Temporary controller to help repair corrupted user profile data
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class DataRepairController : ControllerBase
    {
        private readonly ILogger<DataRepairController> _logger;
        private readonly IContainerFactory _containerFactory;

        public DataRepairController(ILogger<DataRepairController> logger, IContainerFactory containerFactory)
        {
            _logger = logger;
            _containerFactory = containerFactory;
        }

        /// <summary>
        /// Get raw user data to inspect corruption
        /// </summary>
        [HttpGet("raw-user/{userId}")]
        public async Task<IActionResult> GetRawUserData(string userId)
        {
            try
            {
                var container = _containerFactory.CreateCollection<UserProfile>(out _);
                var response = await container.ReadItemStreamAsync(userId, new PartitionKey(userId));

                if (response.IsSuccessStatusCode)
                {
                    using var streamReader = new StreamReader(response.Content);
                    var rawData = await streamReader.ReadToEndAsync();
                    
                    _logger.LogInformation($"Raw data retrieved for {userId}");
                    
                    return Ok(new { 
                        userId = userId,
                        rawData = rawData,
                        status = "success"
                    });
                }
                else
                {
                    return NotFound(new { 
                        userId = userId,
                        error = $"User not found: {response.StatusCode}",
                        status = "not_found"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting raw data for {userId}: {ex.Message}");
                return StatusCode(500, new { 
                    userId = userId,
                    error = ex.Message,
                    status = "error"
                });
            }
        }

        /// <summary>
        /// Attempt to fix corrupted user data
        /// </summary>
        [HttpPost("fix-user/{userId}")]
        public async Task<IActionResult> FixUserData(string userId)
        {
            try
            {
                var container = _containerFactory.CreateCollection<UserProfile>(out _);
                var response = await container.ReadItemStreamAsync(userId, new PartitionKey(userId));

                if (!response.IsSuccessStatusCode)
                {
                    return NotFound(new { userId = userId, error = "User not found" });
                }

                using var streamReader = new StreamReader(response.Content);
                var rawData = await streamReader.ReadToEndAsync();
                
                _logger.LogInformation($"Original corrupted data for {userId}: {rawData}");

                // Parse as JObject to manually fix issues
                var jsonObject = JObject.Parse(rawData);
                
                // Fix common corruption issues
                bool wasFixed = false;
                
                // Fix acceptedDisclaimerDate if it's corrupted
                if (jsonObject["acceptedDisclaimerDate"] != null)
                {
                    var disclaimerValue = jsonObject["acceptedDisclaimerDate"].ToString();
                    if (disclaimerValue.Contains("{") || disclaimerValue.Contains("}") || disclaimerValue.Length < 10)
                    {
                        _logger.LogWarning($"Fixing corrupted acceptedDisclaimerDate for {userId}: {disclaimerValue}");
                        jsonObject["acceptedDisclaimerDate"] = null; // Set to null for now
                        wasFixed = true;
                    }
                }

                // Fix lastLoginDate if it's corrupted
                if (jsonObject["lastLoginDate"] != null)
                {
                    var loginValue = jsonObject["lastLoginDate"].ToString();
                    if (loginValue.Contains("{") || loginValue.Contains("}") || loginValue.Length < 10)
                    {
                        _logger.LogWarning($"Fixing corrupted lastLoginDate for {userId}: {loginValue}");
                        jsonObject["lastLoginDate"] = null; // Set to null for now
                        wasFixed = true;
                    }
                }

                // Fix any other date fields that might be corrupted
                foreach (var property in jsonObject.Properties())
                {
                    if (property.Name.ToLower().Contains("date") && property.Value.Type == JTokenType.String)
                    {
                        var value = property.Value.ToString();
                        if (value.Contains("{") || value.Contains("}"))
                        {
                            _logger.LogWarning($"Fixing corrupted date field {property.Name} for {userId}: {value}");
                            property.Value = null;
                            wasFixed = true;
                        }
                    }
                }

                if (wasFixed)
                {
                    // Convert back to UserProfile object to validate
                    var fixedJson = jsonObject.ToString();
                    var userProfile = JsonConvert.DeserializeObject<UserProfile>(fixedJson);
                    
                    // Save the fixed data back
                    var updateResponse = await container.ReplaceItemAsync(userProfile, userId, new PartitionKey(userId));
                    
                    _logger.LogInformation($"Successfully fixed and saved user data for {userId}");
                    
                    return Ok(new {
                        userId = userId,
                        status = "fixed",
                        message = "User data has been repaired",
                        fixedData = userProfile
                    });
                }
                else
                {
                    return Ok(new {
                        userId = userId,
                        status = "no_issues",
                        message = "No corruption detected in user data"
                    });
                }
            }
            catch (JsonReaderException ex)
            {
                _logger.LogError($"JSON parsing error for {userId}: {ex.Message}");
                return StatusCode(500, new {
                    userId = userId,
                    error = "Data is too corrupted to automatically fix",
                    details = ex.Message,
                    suggestion = "Manual intervention required"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error fixing user data for {userId}: {ex.Message}");
                return StatusCode(500, new {
                    userId = userId,
                    error = ex.Message,
                    status = "error"
                });
            }
        }

        /// <summary>
        /// Create a backup of user data before fixing
        /// </summary>
        [HttpPost("backup-user/{userId}")]
        public async Task<IActionResult> BackupUserData(string userId)
        {
            try
            {
                var container = _containerFactory.CreateCollection<UserProfile>(out _);
                var response = await container.ReadItemStreamAsync(userId, new PartitionKey(userId));

                if (response.IsSuccessStatusCode)
                {
                    using var streamReader = new StreamReader(response.Content);
                    var rawData = await streamReader.ReadToEndAsync();
                    
                    // Log the backup
                    _logger.LogInformation($"BACKUP for {userId}: {rawData}");
                    
                    return Ok(new {
                        userId = userId,
                        backup = rawData,
                        timestamp = DateTime.UtcNow,
                        status = "backed_up"
                    });
                }
                else
                {
                    return NotFound(new { userId = userId, error = "User not found" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error backing up user data for {userId}: {ex.Message}");
                return StatusCode(500, new { error = ex.Message });
            }
        }
    }
}
