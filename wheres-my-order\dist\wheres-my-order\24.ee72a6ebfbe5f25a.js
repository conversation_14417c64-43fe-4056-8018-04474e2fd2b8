"use strict";(self.webpackChunkwheres_my_order=self.webpackChunkwheres_my_order||[]).push([[24],{8024:(Oe,x,a)=>{a.r(x),a.d(x,{FsmModule:()=>he});var m=a(36895),b=a(69201),Z=a(51679),O=a(98350),k=a(61978),v=a(47259),e=a(98274);let F=(()=>{class r{constructor(){}}return r.\u0275fac=function(t){return new(t||r)},r.\u0275cmp=e.Xpm({type:r,selectors:[["app-fsm"]],decls:1,vars:0,template:function(t,n){1&t&&e._UZ(0,"router-outlet")},dependencies:[O.lC],styles:["[_nghost-%COMP%]{width:100%}"]}),r})();var N=a(58551),q=a(61135),C=a(92340),T=a(80529);let I=(()=>{class r{constructor(t){this._http=t,this._initialConfig={type:"report",id:void 0,embedUrl:void 0,accessToken:void 0,tokenType:N.models.TokenType.Embed},this._config=new q.X(this._initialConfig),this.config$=this._config.asObservable()}ngOnInit(){this._http.get(`${C.N.api.url}/PowerBI`).subscribe(t=>{const n={...this._config.value,id:t.EmbedReport[0].ReportId,embedUrl:t.EmbedReport[0].EmbedUrl,accessToken:t.EmbedToken.token};this._config.next(n)})}}return r.\u0275fac=function(t){return new(t||r)(e.Y36(T.eN))},r.\u0275cmp=e.Xpm({type:r,selectors:[["app-dashboard"]],decls:3,vars:4,consts:[[1,"dx-card","content-block","responsive-paddings"],[3,"embedConfig","cssClassName"]],template:function(t,n){1&t&&(e.TgZ(0,"div",0),e._UZ(1,"powerbi-report",1),e.ALo(2,"async"),e.qZA()),2&t&&(e.xp6(1),e.Q6J("embedConfig",e.lcZ(2,2,n.config$))("cssClassName","report"))},dependencies:[b.$i,m.Ov],styles:["[_nghost-%COMP%]{height:100%;width:100%}[_nghost-%COMP%]   .dx-card[_ngcontent-%COMP%]{height:100%}[_nghost-%COMP%]   .dx-card[_ngcontent-%COMP%]   .iframe-container[_ngcontent-%COMP%]{height:100%}  .report{height:100%}"]}),r})();var U=a(15861),w=a(60305),$=a(88744),h=a(5128),M=a(4707),y=a(39841),u=a(54004),R=a(63900),S=a(28746),f=a(83582);let _=(()=>{class r{constructor(t){if(t)for(const[n,o]of Object.entries(t))this[n]=o}static canRevertStatus(t){return!1}static canProgressStatus(t){return"Open - Unscheduled"===t.systemStatus||"Open - Scheduled"===t.systemStatus||"Open - In Progress"===t.systemStatus}static nextStatus(t){switch(t.systemStatus){case"Open - Unscheduled":case"Open - Scheduled":return"Open - In Progress";case"Open - In Progress":return"Open - Completed";default:return}}static revertStatuses(t){if((0,f.le)(t.systemStatus)||"Closed - Posted"===t.systemStatus||"Closed - Canceled"===t.systemStatus||"Open - Scheduled"===t.systemStatus||!this.statuses.includes(t.systemStatus))return[];const n=["Open - Unscheduled","Open - In Progress","Open - Completed"];return n.slice(0,n.indexOf(t.systemStatus))}static stepsFor(t){let n,o,s;switch(t.systemStatus){case"Open - Unscheduled":n="active",o="active",s="active";break;case"Open - Scheduled":n="complete",o="active",s="active";break;case"Open - In Progress":n="complete",o="in-progress",s="active";break;case"Open - Completed":n="complete",o="complete",s="complete";break;case"Closed - Posted":case"Closed - Canceled":n="disabled",o="disabled",s="alert";break;default:n="alert",o="alert",s="alert"}return[{status:n,text:"Acknowledge Request"},{status:o,text:"Work in Progress"},{status:s,text:"alert"===s?"Canceled":"Notify of Completion"}]}}return r.statuses=["Open - Unscheduled","Open - Scheduled","Open - In Progress","Open - Completed","Closed - Posted","Closed - Canceled"],r})();var A=a(63326),J=a(39646),D=a(34782),P=a(12739);let Q=(()=>{class r{constructor(t){this._http=t,this._tradeRequests=new M.t,this._annotationsCache=new Map,this.getAllTradeRequests().subscribe(n=>this._tradeRequests.next(n))}get tradeRequests$(){return this._tradeRequests.asObservable()}getAnnotations(t,n=!1){if(!n&&this._annotationsCache.has(t.id))return(0,J.of)(this._annotationsCache.get(t.id));{const o=this._http.get(`${C.N.api.url}/FieldServiceManagement/Annotations/${t.id}`).pipe((0,u.U)(s=>s.entities.map(c=>({...c,subject:c.attributes.find(g=>"subject"===g.key)?.value,noteText:c.attributes.find(g=>"notetext"===g.key)?.value,createdOn:c.attributes.find(g=>"createdon"===g.key)?.value,createdBy:c.attributes.find(g=>"createdby"===g.key)?.value?.name,documentBody:c.attributes.find(g=>"documentbody"===g.key)?.value,fileName:c.attributes.find(g=>"filename"===g.key)?.value}))),(0,u.U)(s=>s.sort((c,g)=>new Date(c.createdOn).getTime()-new Date(g.createdOn).getTime())),(0,P.fF)(`ANNOTATIONS for ${t.id}`),(0,D.d)(1));return o.subscribe(s=>this._annotationsCache.set(t.id,s)),o}}getAllTradeRequests(){return this._http.get(`${C.N.api.url}/FieldServiceManagement/All`).pipe((0,P.fF)("TRADE REQUESTS"),(0,u.U)(t=>t.entities.map(n=>({...n,drawingNumber:n.attributes.find(o=>"team_drawingnumber"===o.key)?.value,systemStatus:n.formattedValues.find(o=>"msdyn_systemstatus"===o.key)?.value,unit:n.attributes.find(o=>"team_unit"===o.key)?.value,dueDate:n.attributes.find(o=>"team_duedate"===o.key)?.value,createdOn:n.attributes.find(o=>"createdon"===o.key)?.value,scopeOfInspection:n.attributes.find(o=>"team_scopeofinspection"===o.key)?.value,name:n.attributes.find(o=>"msdyn_name"===o.key)?.value,workOrderSummary:n.attributes.find(o=>"msdyn_workordersummary"===o.key)?.value,ourFormattedValues:this.ourFormattedValues(n.formattedValues)}))),(0,P.fF)("TRADE REQUESTS - after transformation"))}updateStatus(t,n,o){const s=this._http.post(`${C.N.api.url}/FieldServiceManagement/Status`,{workRequestNumber:t,status:n,...o}).pipe((0,D.d)());return s.pipe((0,R.w)(()=>this.getAllTradeRequests())).subscribe(c=>this._tradeRequests.next(c)),s}addComment(t,n){return this._http.post(`${C.N.api.url}/FieldServiceManagement/Comments`,{workRequestNumber:t,comment:n})}ourFormattedValues(t){const n={};for(const o of t)n[o.key]=o.value;return n}}return r.\u0275fac=function(t){return new(t||r)(e.LFG(T.eN))},r.\u0275prov=e.Yz7({token:r,factory:r.\u0275fac,providedIn:"root"}),r})();var E=a(97185),z=a(15697),L=a(52599),j=a(87074),B=a(94064),K=a(62688),Y=a(96696),G=a(99106),p=a(84943);function H(r,i){if(1&r&&(e.TgZ(0,"span",5)(1,"h3",6),e._uU(2),e.qZA(),e._uU(3),e.qZA()),2&r){const t=e.oxw().$implicit;e.xp6(2),e.Oqu(t.text),e.xp6(1),e.hij(" ",t.summary," ")}}function X(r,i){if(1&r&&(e.TgZ(0,"li",1),e._UZ(1,"span",3),e.YNc(2,H,4,2,"span",4),e.qZA()),2&r){const t=i.$implicit,n=e.oxw(2);e.Q6J("ngClass",n.stepClass(t)),e.xp6(2),e.Q6J("ngIf",t.text)}}function V(r,i){if(1&r&&(e.TgZ(0,"ul",1),e.YNc(1,X,3,2,"li",2),e.qZA()),2&r){const t=e.oxw();e.Q6J("ngClass",t.trackerClass),e.xp6(1),e.Q6J("ngForOf",t.steps)}}let W=(()=>{class r{constructor(){this.steps=[]}get trackerClass(){return{"progress-tracker":!0,"progress-tracker--vertical":this.vertical,"progress-tracker--center":"center"===this.alignment,"progress-tracker--right":"right"===this.alignment,"progress-tracker--spaced":this.spaced,"progress-tracker--border":this.border,"progress-tracker--text":this.steps.some(t=>t.text)}}stepClass(t){return{"progress-step":!0,"is-complete":"complete"===t.status,"is-active":"active"===t.status,"is-progress":"in-progress"===t.status,"is-disabled":"disabled"===t.status,"is-alert":"alert"===t.status,"is-canceled":"canceled"===t.status}}}return r.\u0275fac=function(t){return new(t||r)},r.\u0275cmp=e.Xpm({type:r,selectors:[["app-progress-tracker"]],inputs:{steps:"steps",alignment:"alignment",spaced:"spaced",border:"border",vertical:"vertical"},decls:1,vars:1,consts:[[3,"ngClass",4,"ngIf"],[3,"ngClass"],[3,"ngClass",4,"ngFor","ngForOf"],[1,"progress-marker"],["class","progress-text",4,"ngIf"],[1,"progress-text"],[1,"progress-title"]],template:function(t,n){1&t&&e.YNc(0,V,2,2,"ul",0),2&t&&e.Q6J("ngIf",n.steps)},dependencies:[m.mk,m.sg,m.O5],styles:['.progress-tracker[_ngcontent-%COMP%]{display:flex;margin:60px auto;padding:0;list-style:none}.progress-step[_ngcontent-%COMP%]{flex:1 1 0%;margin:0;padding:0;min-width:24px}.progress-step[_ngcontent-%COMP%]:last-child{flex-grow:0}.progress-step[_ngcontent-%COMP%]:last-child   .progress-marker[_ngcontent-%COMP%]:after{display:none}.progress-link[_ngcontent-%COMP%], .progress-marker[_ngcontent-%COMP%]{display:block;position:relative}.progress-marker[_ngcontent-%COMP%]:before{font-family:FontAwesome;display:flex;position:relative;z-index:20;width:24px;height:24px;padding-bottom:2px;border-radius:50%;transition:background-color,border-color;transition-duration:.3s;align-items:center;justify-content:center}.progress-marker[_ngcontent-%COMP%]:after{content:"";display:block;position:absolute;top:10px;right:-12px;width:100%;height:4px;transition:background-color .3s,background-position .3s}.progress-text[_ngcontent-%COMP%]{display:block;padding:8px;overflow:hidden;text-overflow:ellipsis}.progress-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin-bottom:0}.progress-title[_ngcontent-%COMP%]{margin-top:0}.progress-step[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]{color:#fff}.progress-step[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:before, .progress-step[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{background-color:#b6b6b6;padding-top:4px}.progress-step[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]{color:#333}.progress-step.is-active[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:before{background-color:#2196f3;content:"\\f054"}.progress-step.is-complete[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:before{background-color:green;content:"\\f00c"}.progress-step.is-alert[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:before{background-color:red;content:"\\f12a"}.progress-step.is-progress[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:before{background-color:#1976d2;content:"\\f017";padding-right:1px}.progress-step.is-disabled[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:before{background-color:#868686;content:"\\f05e"}.progress-step.is-canceled[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:before{background-color:#bfbfbf;content:""}.progress-step.is-disabled[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]{color:#868686}.progress-step.is-canceled[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]{color:#bfbfbf}.progress-step.is-complete[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after, .progress-step.is-progress[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after, .progress-step.is-disabled[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after, .progress-step.is-alert[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after, .progress-step.is-canceled[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{background-color:#868686}.progress-step.is-progress-10[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{background-image:linear-gradient(to right,#868686 10%,#b6b6b6 10%)}.progress-step.is-progress-20[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{background-image:linear-gradient(to right,#868686 20%,#b6b6b6 20%)}.progress-step.is-progress-30[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{background-image:linear-gradient(to right,#868686 30%,#b6b6b6 30%)}.progress-step.is-progress-40[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{background-image:linear-gradient(to right,#868686 40%,#b6b6b6 40%)}.progress-step.is-progress-50[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{background-image:linear-gradient(to right,#868686 50%,#b6b6b6 50%)}.progress-step.is-progress-60[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{background-image:linear-gradient(to right,#868686 60%,#b6b6b6 60%)}.progress-step.is-progress-70[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{background-image:linear-gradient(to right,#868686 70%,#b6b6b6 70%)}.progress-step.is-progress-80[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{background-image:linear-gradient(to right,#868686 80%,#b6b6b6 80%)}.progress-step.is-progress-90[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{background-image:linear-gradient(to right,#868686 90%,#b6b6b6 90%)}.progress-step[_ngcontent-%COMP%]:hover   .progress-marker[_ngcontent-%COMP%]:before{background-color:#56adf5}.progress-step.is-complete[_ngcontent-%COMP%]:hover   .progress-marker[_ngcontent-%COMP%]:before{background-color:#6c8f6c}.progress-step.is-alert[_ngcontent-%COMP%]:hover   .progress-marker[_ngcontent-%COMP%]:before{background-color:#d36666}.progress-tracker--text[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]:last-child, .progress-tracker--center[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]:last-child, .progress-tracker--right[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]:last-child{flex-grow:1}.progress-tracker--center[_ngcontent-%COMP%]{text-align:center}.progress-tracker--center[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:before, .progress-tracker--center[_ngcontent-%COMP%]   .progress-text--dotted[_ngcontent-%COMP%]:before{margin-left:auto;margin-right:auto}.progress-tracker--center[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{right:-50%}.progress-tracker--right[_ngcontent-%COMP%]{text-align:right}.progress-tracker--right[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:before, .progress-tracker--right[_ngcontent-%COMP%]   .progress-text--dotted[_ngcontent-%COMP%]:before{margin-left:auto}.progress-tracker--right[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{right:calc(-100% + 12px)}.progress-tracker--spaced[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{width:calc(100% - 40px);margin-left:20px;margin-right:20px}.progress-tracker--border[_ngcontent-%COMP%]{padding:4px;border:2px solid #333;border-radius:32px}.progress-tracker--theme-red[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]{color:#fff}.progress-tracker--theme-red[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:before, .progress-tracker--theme-red[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{background-color:#666}.progress-tracker--theme-red[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]{color:#333}.progress-tracker--theme-red[_ngcontent-%COMP%]   .progress-step.is-active[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:before{background-color:#a62d24}.progress-tracker--theme-red[_ngcontent-%COMP%]   .progress-step.is-complete[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:before{background-color:#d93b30}.progress-tracker--theme-red[_ngcontent-%COMP%]   .progress-step.is-complete[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{background-color:#333}.progress-tracker--theme-red[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]:hover   .progress-marker[_ngcontent-%COMP%]:before{background-color:#df7b74}.progress-text--dotted[_ngcontent-%COMP%]:before{content:"";display:block;width:12px;height:12px;margin:6px -2px;background-size:12px 18px;background-image:repeating-radial-gradient(circle at center 6px,#b6b6b6,#b6b6b6 5px,rgba(182,182,182,.5) 5.5px,rgba(182,182,182,.01) 6px,transparent 100%)}.progress-text--dotted-1[_ngcontent-%COMP%]:before{height:12px}.progress-text--dotted-2[_ngcontent-%COMP%]:before{height:30px}.progress-text--dotted-3[_ngcontent-%COMP%]:before{height:48px}.progress-text--dotted-4[_ngcontent-%COMP%]:before{height:66px}.progress-text--dotted-5[_ngcontent-%COMP%]:before{height:84px}.progress-text--dotted-6[_ngcontent-%COMP%]:before{height:102px}.progress-text--dotted-7[_ngcontent-%COMP%]:before{height:120px}.progress-text--dotted-8[_ngcontent-%COMP%]:before{height:138px}.progress-text--dotted-9[_ngcontent-%COMP%]:before{height:156px}.progress-text--dotted-10[_ngcontent-%COMP%]:before{height:174px}.progress-text--dotted-11[_ngcontent-%COMP%]:before{height:192px}.progress-text--dotted-12[_ngcontent-%COMP%]:before{height:210px}.progress-tracker--text-top[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]{height:100%}.progress-tracker--text-top[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]{top:-24px}.progress-tracker--text-inline[_ngcontent-%COMP%]{overflow:hidden}.progress-tracker--text-inline[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]{display:flex;align-items:center}.progress-tracker--text-inline[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]{display:flex;align-items:center;flex-grow:1}.progress-tracker--text-inline[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{top:auto}.progress-tracker--text-inline[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]{position:relative;z-index:30;max-width:70%;white-space:nowrap;padding-top:0;padding-bottom:0;background-color:#fff}.progress-tracker--text-inline[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]{display:inline-block}.progress-tracker--text-inline[_ngcontent-%COMP%]   .progress-title[_ngcontent-%COMP%]{margin:0}.progress-tracker--square[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:before{border-radius:0}.progress-tracker--square[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{top:auto;bottom:0}@media (max-width: 575px){.progress-tracker-wrapper[_ngcontent-%COMP%]{overflow-x:auto;scroll-snap-type:x proximity}.progress-tracker-wrapper[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]{min-width:50%;scroll-snap-align:start}}.progress-tracker--vertical[_ngcontent-%COMP%]{flex-direction:column}.progress-tracker--vertical[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]{display:flex;flex:1 1 auto}.progress-tracker--vertical.progress-tracker--right[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]{flex-direction:row-reverse}.progress-tracker--vertical[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{right:auto;top:12px;left:10px;width:4px;height:100%}.progress-tracker--vertical[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]{padding:0 12px 24px}@keyframes scale-up{0%{opacity:1;transform:translate(-50%,-50%) scale(0)}to{opacity:0;transform:translate(-50%,-50%) scale(1)}}.anim-ripple[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .anim-ripple-large[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .anim-ripple-splash[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before{content:"";display:block;width:24px;height:24px;position:absolute;top:12px;left:12px;z-index:30;background:rgba(0,0,0,.3);border-radius:50%;transform:translate(-50%,-50%) scale(0);visibility:hidden}.anim-ripple[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:not(:active):before, .anim-ripple-large[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:not(:active):before, .anim-ripple-splash[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:not(:active):before{animation:scale-up .3s ease-out}.anim-ripple[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:focus:before, .anim-ripple-large[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:focus:before, .anim-ripple-splash[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:focus:before{visibility:visible}.anim-ripple.progress-tracker--center[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .anim-ripple.progress-tracker--center[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:after{left:50%}.progress-tracker--center[_ngcontent-%COMP%]   .anim-ripple[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .progress-tracker--center[_ngcontent-%COMP%]   .anim-ripple[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:after{left:50%}.anim-ripple-large.progress-tracker--center[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .anim-ripple-large.progress-tracker--center[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:after{left:50%}.progress-tracker--center[_ngcontent-%COMP%]   .anim-ripple-large[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .progress-tracker--center[_ngcontent-%COMP%]   .anim-ripple-large[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:after{left:50%}.anim-ripple-splash.progress-tracker--center[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .anim-ripple-splash.progress-tracker--center[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:after{left:50%}.progress-tracker--center[_ngcontent-%COMP%]   .anim-ripple-splash[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .progress-tracker--center[_ngcontent-%COMP%]   .anim-ripple-splash[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:after{left:50%}.anim-ripple-double.progress-tracker--center[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .anim-ripple-double.progress-tracker--center[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:after{left:50%}.progress-tracker--center[_ngcontent-%COMP%]   .anim-ripple-double[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .progress-tracker--center[_ngcontent-%COMP%]   .anim-ripple-double[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:after{left:50%}.anim-ripple.progress-tracker--right[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .anim-ripple.progress-tracker--right[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:after{left:calc(100% - 12px)}.progress-tracker--right[_ngcontent-%COMP%]   .anim-ripple[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .progress-tracker--right[_ngcontent-%COMP%]   .anim-ripple[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:after{left:calc(100% - 12px)}.anim-ripple-large.progress-tracker--right[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .anim-ripple-large.progress-tracker--right[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:after{left:calc(100% - 12px)}.progress-tracker--right[_ngcontent-%COMP%]   .anim-ripple-large[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .progress-tracker--right[_ngcontent-%COMP%]   .anim-ripple-large[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:after{left:calc(100% - 12px)}.anim-ripple-splash.progress-tracker--right[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .anim-ripple-splash.progress-tracker--right[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:after{left:calc(100% - 12px)}.progress-tracker--right[_ngcontent-%COMP%]   .anim-ripple-splash[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .progress-tracker--right[_ngcontent-%COMP%]   .anim-ripple-splash[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:after{left:calc(100% - 12px)}.anim-ripple-double.progress-tracker--right[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .anim-ripple-double.progress-tracker--right[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:after{left:calc(100% - 12px)}.progress-tracker--right[_ngcontent-%COMP%]   .anim-ripple-double[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .progress-tracker--right[_ngcontent-%COMP%]   .anim-ripple-double[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:after{left:calc(100% - 12px)}.anim-ripple-splash[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before{width:48px;height:48px;box-shadow:0 0 6px 6px #00000059}.anim-ripple-double[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .anim-ripple-double[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:after{content:"";display:block;width:24px;height:24px;position:absolute;top:12px;left:12px;z-index:30;background:rgba(0,0,0,.3);border-radius:50%;transform:translate(-50%,-50%) scale(0);visibility:hidden;background:none;border:3px solid rgba(0,0,0,.3)}.anim-ripple-double[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:not(:active):before{animation:scale-up .3s ease-out 0s}.anim-ripple-double[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:not(:active):after{animation:scale-up .3s ease-out 0s;animation-delay:.15s}.anim-ripple-double[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:focus:before, .anim-ripple-double[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:focus:after{visibility:visible}.anim--large[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:before, .anim--large[_ngcontent-%COMP%]   .progress-link[_ngcontent-%COMP%]:after{width:48px;height:48px}.anim--path[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{background-image:linear-gradient(to right,#b6b6b6 50%,#868686 50%);background-size:200% 100%;background-position:0% 100%;transition:background-position .3s ease-out}.progress-step.is-complete[_ngcontent-%COMP%]   .anim--path[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after, .anim--path[_ngcontent-%COMP%]   .progress-step.is-complete[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{background-position:-100% 100%}[dir=rtl][_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{right:auto;left:-12px}[dir=rtl][_ngcontent-%COMP%]   .progress-tracker--center[_ngcontent-%COMP%]   .progress-marker[_ngcontent-%COMP%]:after{left:-50%}']}),r})();var ee=a(52031),te=a(11481);let ne=(()=>{class r{constructor(t){this.sanitizer=t}transform(t,n){switch(n){case"html":return this.sanitizer.bypassSecurityTrustHtml(t);case"style":return this.sanitizer.bypassSecurityTrustStyle(t);case"script":return this.sanitizer.bypassSecurityTrustScript(t);case"url":return this.sanitizer.bypassSecurityTrustUrl(t);case"resourceUrl":return this.sanitizer.bypassSecurityTrustResourceUrl(t);default:throw new Error(`Invalid safe type specified: ${n}`)}}}return r.\u0275fac=function(t){return new(t||r)(e.Y36(te.H7,16))},r.\u0275pipe=e.Yjl({name:"safe",type:r,pure:!0}),r})();function re(r,i){if(1&r&&(e.TgZ(0,"div",0)(1,"div",34),e._UZ(2,"dx-text-area",35),e.qZA()()),2&r){const t=e.oxw().ngIf;e.xp6(2),e.Q6J("value",t.workOrderSummary)("readOnly",!0)}}function oe(r,i){if(1&r){const t=e.EpF();e.TgZ(0,"dx-button",51),e.NdJ("onClick",function(){e.CHM(t);const o=e.oxw().$implicit,s=e.oxw(3);return e.KtG(s.onAnnotationFileShow(o.documentBody))}),e.qZA()}if(2&r){const t=e.oxw().$implicit;e.Q6J("text",t.fileName)}}function se(r,i){if(1&r&&(e.TgZ(0,"div",43)(1,"div",44),e.ALo(2,"async"),e.TgZ(3,"h6",45),e._uU(4),e.qZA(),e.YNc(5,oe,1,1,"dx-button",46),e._UZ(6,"p",47),e.ALo(7,"safe"),e.TgZ(8,"div",48)(9,"strong"),e._uU(10),e.qZA(),e._UZ(11,"span",49),e.TgZ(12,"span",50),e._uU(13),e.ALo(14,"amTimeAgo"),e.ALo(15,"date"),e.qZA()()()()),2&r){const t=i.$implicit,n=e.oxw(3);let o;e.xp6(1),e.ekj("me",t.userEmail===(null==(o=e.lcZ(2,8,n.currentProfile$))?null:o.id)),e.xp6(3),e.hij("",t.subject," "),e.xp6(1),e.Q6J("ngIf",t.documentBody),e.xp6(1),e.Q6J("innerHTML",e.xi3(7,10,t.noteText,"html"),e.oJD),e.xp6(4),e.Oqu(t.createdBy),e.xp6(3),e.AsE(" ",e.lcZ(14,13,t.createdOn),"\xa0 (",e.xi3(15,15,t.createdOn,"short"),") ")}}function ae(r,i){if(1&r){const t=e.EpF();e.TgZ(0,"div",0)(1,"div",36)(2,"dx-scroll-view",37),e.YNc(3,se,16,18,"div",38),e.ALo(4,"async"),e.qZA(),e.TgZ(5,"dx-text-area",39),e.NdJ("valueChange",function(o){e.CHM(t);const s=e.oxw(2);return e.KtG(s.newNote=o)}),e.qZA(),e.TgZ(6,"div",40)(7,"dx-button",41),e.NdJ("onClick",function(o){e.CHM(t);const s=e.oxw(2);return e.KtG(s.clearNote(o))}),e.qZA(),e.TgZ(8,"dx-button",42),e.NdJ("onClick",function(o){e.CHM(t);const s=e.oxw(2);return e.KtG(s.submitNote(o))}),e.qZA()()()()}if(2&r){const t=e.oxw(2);e.xp6(2),e.Q6J("height",400)("showScrollbar","always"),e.xp6(1),e.Q6J("ngForOf",e.lcZ(4,6,t.focusedAnnotations$)),e.xp6(2),e.Q6J("value",t.newNote),e.xp6(2),e.Q6J("disabled",!t.newNote||t.savingNote),e.xp6(1),e.Q6J("disabled",!t.newNote||t.savingNote)}}function ie(r,i){1&r&&e._UZ(0,"i",52)}const ce=function(){return{width:200}};function ge(r,i){if(1&r){const t=e.EpF();e.TgZ(0,"dx-drop-down-button",53),e.NdJ("onItemClick",function(o){e.CHM(t);const s=e.oxw(2);return e.KtG(s.onRevertClicked(o))}),e.ALo(1,"async"),e.qZA()}if(2&r){const t=i.ngIf,n=e.oxw(2);e.Q6J("dropDownOptions",e.DdM(5,ce))("items",t)("disabled",e.lcZ(1,3,n.disableRevertStatusDropDown$))}}const pe=function(){return{title:"Trade Directions",template:"trade-directions-tab"}},le=function(){return{title:"Trade Comments",template:"trade-comments-tab"}},de=function(r,i){return[r,i]};function ue(r,i){if(1&r){const t=e.EpF();e.TgZ(0,"div",22)(1,"h4"),e._uU(2,"Details"),e.qZA(),e.TgZ(3,"div",23)(4,"section",24)(5,"label",25),e._uU(6,"Asset/Drawing:"),e.qZA(),e.TgZ(7,"span"),e._uU(8),e.qZA()(),e._UZ(9,"app-progress-tracker",26),e.ALo(10,"async"),e.qZA(),e.TgZ(11,"div",27)(12,"dx-tab-panel",28),e.YNc(13,re,3,2,"div",29),e.YNc(14,ae,9,8,"div",29),e.qZA()(),e.TgZ(15,"div",30)(16,"dx-button",31),e.NdJ("onClick",function(){e.CHM(t);const o=e.oxw();return e.KtG(o.nextStatusClicked())}),e.ALo(17,"async"),e.ALo(18,"async"),e.ALo(19,"async"),e.qZA(),e.YNc(20,ie,1,0,"i",32),e.ALo(21,"async"),e.YNc(22,ge,2,6,"dx-drop-down-button",33),e.ALo(23,"async"),e.qZA()()}if(2&r){const t=i.ngIf,n=e.oxw();e.xp6(8),e.Oqu(t.drawingNumber),e.xp6(1),e.Q6J("steps",e.lcZ(10,11,n.progressSteps$))("alignment","center"),e.xp6(3),e.Q6J("items",e.WLB(25,de,e.DdM(23,pe),e.DdM(24,le))),e.xp6(1),e.Q6J("dxTemplateOf","trade-directions-tab"),e.xp6(1),e.Q6J("dxTemplateOf","trade-comments-tab"),e.xp6(2),e.Q6J("text",e.lcZ(17,13,n.nextStatusButtonText$))("disabled",e.lcZ(18,15,n.disableNextStatusButton$)||e.lcZ(19,17,n.pendingRequest$))("type","Open - In Progress"===t.systemStatus?"success":"default"),e.xp6(4),e.Q6J("ngIf",e.lcZ(21,19,n.pendingRequest$)),e.xp6(2),e.Q6J("ngIf",e.lcZ(23,21,!1))}}const me=function(){return[5,10,25,50]},_e=[{path:"",component:F,children:[{path:"dashboard",component:I,canActivate:[v.jv,k.RQ],data:{pageTitle:"FSM Dashboard"}},{path:"trades-request",component:(()=>{class r{constructor(t,n,o,s){this._users=t,this._fsm=n,this._grid=o,this._toasts=s,this._focusedRow=new M.t,this.now=new Date,this._pendingRequest=new M.t,this.currentProfile$=this._users.currentProfile$,this.tradeRequests$=this._fsm.tradeRequests$,this.focusedTradeRequest$=(0,y.a)([this.tradeRequests$,this._focusedRow]).pipe((0,u.U)(([c,g])=>{const l=c.find(d=>d.id===g);return this._focusedRowData=l,l})),this.progressSteps$=this.focusedTradeRequest$.pipe((0,u.U)(c=>_.stepsFor(c))),this.revertOptions$=this.focusedTradeRequest$.pipe((0,u.U)(c=>_.revertStatuses(c))),this.nextStatusButtonText$=this.focusedTradeRequest$.pipe((0,u.U)(c=>_.nextStatus(c)),(0,u.U)(c=>{switch(c){case"Open - Unscheduled":case"Open - Scheduled":return"Acknowledge Request";case"Open - In Progress":return"Mark as In Progress";case"Open - Completed":return"Notify of Completion"}})),this.disableNextStatusButton$=this.focusedTradeRequest$.pipe((0,u.U)(c=>!_.canProgressStatus(c))),this.disableRevertStatusDropDown$=(0,y.a)([this.focusedTradeRequest$,this.revertOptions$]).pipe((0,u.U)(([c,g])=>!_.canRevertStatus(c)||g?.length<=0)),this.savingNote=!1,this.startTimeValidation=c=>{if(!this.form)return;const g=this.form.instance.option("formData").startTime,l=this.form.instance.option("formData").arrivalTime,d=this.form.instance.option("formData").endTime;return g&&l&&d&&g.getTime()<l.getTime()&&g.getTime()<d.getTime()},this.arrivalTimeValidation=c=>{if(!this.form)return;const g=this.form.instance.option("formData").startTime,l=this.form.instance.option("formData").arrivalTime,d=this.form.instance.option("formData").endTime;return g&&l&&d&&l.getTime()>g.getTime()&&l.getTime()<d.getTime()},this.endTimeValidation=c=>{if(!this.form)return;const g=this.form.instance.option("formData").startTime,l=this.form.instance.option("formData").arrivalTime,d=this.form.instance.option("formData").endTime;return g&&l&&d&&d.getTime()>g.getTime()&&d.getTime()>l.getTime()},this._pendingRequest.next(!1)}get focusedRowKey(){return this._focusedRowKey}set focusedRowKey(t){this._focusedRowKey=t,this._focusedRow.next(this._focusedRowKey)}get pendingRequest$(){return this._pendingRequest.asObservable()}onFocusedRowChanged(t){this.focusedAnnotations$=this._fsm.getAnnotations(t.row.data),this._focusedRowData=t.row.data,this.focusedAnnotations$.subscribe()}onToolbarPreparing(t){var s,n=this;t.toolbarOptions.items.unshift({widget:"dxButton",options:{icon:"fa fa-undo",hint:"Restore Grid Defaults",onClick:(s=(0,U.Z)(function*(){(yield n._grid.resetGridState(n.dataGrid))&&localStorage.removeItem("fsmGridState")}),function(){return s.apply(this,arguments)})},location:"after"})}onRevertClicked(t){}submitNote(t){this.savingNote=!0,this._fsm.addComment(this._focusedRowData.name,this.newNote).pipe((0,R.w)(n=>(this.handleFSMMessageResponse(n),this.focusedAnnotations$=this._fsm.getAnnotations(this._focusedRowData,!0),this.focusedAnnotations$)),(0,S.x)(()=>this.savingNote=!1)).subscribe(()=>{this.newNote=""})}clearNote(t){this.newNote=""}nextStatusClicked(){if((0,f.le)(this._focusedRowData.dueDate))(0,h.Z9)("This operation cannot be completed because the Due Date is not filled in. Please contact your TEAM representative to resolve the issue.","Invalid Operation");else{var t=new Date(this._focusedRowData.dueDate);if((t=new Date(t.getFullYear(),t.getMonth(),t.getDate(),23,59,59)).valueOf()<(new Date).valueOf())(0,h.Z9)("This operation cannot be completed because the Due Date is in the past. Please contact your TEAM representative to resolve the issue.","Invalid Operation");else if(!(0,f.le)(this.lastRequestTime)&&!(0,f.le)(this.lastRequestID)&&(new Date).valueOf()-this.lastRequestTime.valueOf()<6e4&&this.lastRequestID===this._focusedRowData.id)(0,h.Z9)("Requests cannot be marked as completed in the same minute they were marked as in progress. Please try again at a later time.","Invalid Operation");else{if("Open - Unscheduled"===this._focusedRowData.systemStatus||"Open - Scheduled"===this._focusedRowData.systemStatus){if((0,f.le)(this._focusedRowData.createdOn))return void(0,h.Z9)("This operation cannot be completed because the Created Date is not filled in. Please contact your TEAM representative to resolve the issue.","Invalid Operation");var n=new Date(this._focusedRowData.createdOn);this.progressStatusOfFocusedRow({startTime:n,arrivalTime:new Date,endTime:t})}else this.progressStatusOfFocusedRow();this.lastRequestTime=new Date,this.lastRequestID=this._focusedRowData.id}}}onAnnotationFileShow(t){const n=new Uint8Array(atob(t).split("").map(c=>c.charCodeAt(0))),o=new Blob([n],{type:"application/pdf"}),s=window.URL.createObjectURL(o);window.open(s,"_blank"),window.URL.revokeObjectURL(s)}onNextStatusFormFieldChanged(t){if(!this.form)return;const n=this.form.instance.option("formData").startTime,o=this.form.instance.option("formData").arrivalTime,s=this.form.instance.option("formData").endTime;n&&o&&s&&t.component.validate()}progressStatusOfFocusedRow(t){const n=_.nextStatus(this._focusedRowData);if("Open - In Progress"===n&&(0,f.le)(t))throw new Error("If moving work request to Open - In Progress, must include dates");this._pendingRequest.next(!0),this._fsm.updateStatus(this._focusedRowData.name,n,t).pipe((0,S.x)(()=>this._pendingRequest.next(!1))).subscribe(o=>{this.handleFSMMessageResponse(o)})}handleFSMMessageResponse(t){t.error?this._toasts.error(t.error):t.Message.toLowerCase().includes("error")?this._toasts.error(t.Message):t.Message&&this._toasts.success(t.Message)}}return r.\u0275fac=function(t){return new(t||r)(e.Y36(A.fz),e.Y36(Q),e.Y36(A.A6),e.Y36(E._W))},r.\u0275cmp=e.Xpm({type:r,selectors:[["app-trades-request"]],viewQuery:function(t,n){if(1&t&&(e.Gf(w.e,5),e.Gf($.Y,5)),2&t){let o;e.iGM(o=e.CRH())&&(n.dataGrid=o.first),e.iGM(o=e.CRH())&&(n.form=o.first)}},decls:29,vars:30,consts:[[1,"responsive-paddings"],[1,"content-block",2,"display","flex","justify-content","left","margin-bottom","10px"],[2,"width","200px","margin","2px",3,"showClearButton","stylingMode"],["siteSelectionBox",""],[1,"main-flex"],[1,"dx-card","responsive-paddings",2,"width","100%"],["keyExpr","id",3,"dataSource","focusedRowEnabled","focusedRowKey","allowColumnReordering","allowColumnResizing","columnAutoWidth","showBorders","focusedRowKeyChange","onFocusedRowChanged","onToolbarPreparing"],[3,"visible"],[3,"useNative"],[3,"pageSize"],[3,"showPageSizeSelector","visible","allowedPageSizes"],[3,"visible","highlightCaseSensitive"],["mode","dragAndDrop",3,"enabled"],["type","localStorage","storageKey","fsmGridState",3,"enabled"],[3,"enabled","allowExportSelectedData"],["caption","Work Request ID","dataType","string","dataField","name"],["caption","Work Type","dataType","string","dataField","scopeOfInspection"],["caption","Due Date","dataType","date","dataField","dueDate"],["caption","Unit","dataType","string","dataField","unit"],["caption","Asset/Drawing","dataType","string","dataField","drawingNumber"],["caption","Completion Status","dataType","string","dataField","systemStatus"],["class","dx-card responsive-paddings details-flex",4,"ngIf"],[1,"dx-card","responsive-paddings","details-flex"],[1,"tracker-flex"],[1,"properties"],["for","drawingNumber"],[2,"flex-grow","1",3,"steps","alignment"],[2,"width","100%"],[2,"width","100%","margin","10px",3,"items"],["class","responsive-paddings",4,"dxTemplate","dxTemplateOf"],[1,"button-flex"],[1,"complete-button",3,"text","disabled","type","onClick"],["class","fa fa-spinner fa-pulse fa-2x",4,"ngIf"],["text","Revert",3,"dropDownOptions","items","disabled","onItemClick",4,"ngIf"],[2,"height","300px","width","100%"],["placeholder","No Trade Directions present",1,"directions-text-area",3,"value","readOnly"],[1,"job-notes-tab"],[1,"notes",3,"height","showScrollbar"],["class","note",4,"ngFor","ngForOf"],["placeholder","Add New Comment",3,"value","valueChange"],[1,"buttons",2,"margin-top","10px"],["text","Cancel",3,"disabled","onClick"],["text","Save","type","success",3,"disabled","onClick"],[1,"note"],[1,"dx-card","responsive-paddings"],[1,"subject"],["icon","file","type","default","stylingMode","text",3,"text","onClick",4,"ngIf"],[1,"note-text",3,"innerHTML"],[1,"note-footer"],[2,"width","20px"],[1,"ago"],["icon","file","type","default","stylingMode","text",3,"text","onClick"],[1,"fa","fa-spinner","fa-pulse","fa-2x"],["text","Revert",3,"dropDownOptions","items","disabled","onItemClick"]],template:function(t,n){1&t&&(e.TgZ(0,"div",0)(1,"h2"),e._uU(2,"Trades Request"),e.qZA(),e.TgZ(3,"div",1),e._UZ(4,"dx-select-box",2,3),e.qZA(),e.TgZ(6,"div",4)(7,"div",5)(8,"dx-data-grid",6),e.NdJ("focusedRowKeyChange",function(s){return n.focusedRowKey=s})("onFocusedRowChanged",function(s){return n.onFocusedRowChanged(s)})("onToolbarPreparing",function(s){return n.onToolbarPreparing(s)}),e.ALo(9,"async"),e._UZ(10,"dxo-group-panel",7)(11,"dxo-scrolling",8)(12,"dxo-paging",9)(13,"dxo-pager",10)(14,"dxo-header-filter",7)(15,"dxo-filter-row",7)(16,"dxo-search-panel",11)(17,"dxo-filter-panel",7)(18,"dxo-column-chooser",12)(19,"dxo-state-storing",13)(20,"dxo-export",14)(21,"dxi-column",15)(22,"dxi-column",16)(23,"dxi-column",17)(24,"dxi-column",18)(25,"dxi-column",19)(26,"dxi-column",20),e.qZA()(),e.YNc(27,ue,24,28,"div",21),e.ALo(28,"async"),e.qZA()()),2&t&&(e.xp6(4),e.Q6J("showClearButton",!1)("stylingMode","filled"),e.xp6(4),e.Q6J("dataSource",e.lcZ(9,25,n.tradeRequests$))("focusedRowEnabled",!0)("focusedRowKey",n.focusedRowKey)("allowColumnReordering",!0)("allowColumnResizing",!0)("columnAutoWidth",!0)("showBorders",!0),e.xp6(2),e.Q6J("visible",!0),e.xp6(1),e.Q6J("useNative",!0),e.xp6(1),e.Q6J("pageSize",5),e.xp6(1),e.Q6J("showPageSizeSelector",!0)("visible",!0)("allowedPageSizes",e.DdM(29,me)),e.xp6(1),e.Q6J("visible",!0),e.xp6(1),e.Q6J("visible",!0),e.xp6(1),e.Q6J("visible",!0)("highlightCaseSensitive",!0),e.xp6(1),e.Q6J("visible",!0),e.xp6(1),e.Q6J("enabled",!0),e.xp6(1),e.Q6J("enabled",!0),e.xp6(1),e.Q6J("enabled",!0)("allowExportSelectedData",!0),e.xp6(7),e.Q6J("ngIf",e.lcZ(28,27,n.focusedTradeRequest$)))},dependencies:[m.sg,m.O5,z.K,G.p6,w.e,p.Auv,p.qvW,p.tZE,p.Ak0,p.ecQ,p.I62,p.Een,p.ilc,p.sXh,p.PXJ,p.XXE,p.C9T,L._,j.N,B.I,K.k,Y.m,W,m.Ov,m.uU,ee.eG,ne],styles:[".notes[_ngcontent-%COMP%]{background-color:#bfbfbf26;border:1px solid #e0e0e0;border-bottom:0}.notes[_ngcontent-%COMP%]   .note[_ngcontent-%COMP%]{display:flex;width:100%;margin-bottom:1em}.notes[_ngcontent-%COMP%]   .note[_ngcontent-%COMP%]   .dx-card[_ngcontent-%COMP%]{width:-moz-max-content;width:max-content;min-width:40%;max-width:70%}.notes[_ngcontent-%COMP%]   .note[_ngcontent-%COMP%]   .dx-card.me[_ngcontent-%COMP%]{margin-left:auto;margin-right:1em}.notes[_ngcontent-%COMP%]   .note[_ngcontent-%COMP%]   .dx-card[_ngcontent-%COMP%]:not(.me){margin-left:1em}.notes[_ngcontent-%COMP%]   .note[_ngcontent-%COMP%]   .dx-card[_ngcontent-%COMP%]   .subject[_ngcontent-%COMP%]{margin-top:0}.notes[_ngcontent-%COMP%]   .note[_ngcontent-%COMP%]   .dx-card[_ngcontent-%COMP%]   .note-text[_ngcontent-%COMP%]{word-wrap:break-word}.notes[_ngcontent-%COMP%]   .note[_ngcontent-%COMP%]   .dx-card[_ngcontent-%COMP%]   .note-footer[_ngcontent-%COMP%]{display:flex;color:#858585;justify-content:space-between}.notes[_ngcontent-%COMP%]   .note[_ngcontent-%COMP%]   .dx-card[_ngcontent-%COMP%]   .note-footer[_ngcontent-%COMP%]   .ago[_ngcontent-%COMP%]{text-align:end;color:#858585}.buttons[_ngcontent-%COMP%]{width:100%;display:flex;justify-content:flex-end}.buttons[_ngcontent-%COMP%]   dx-button[_ngcontent-%COMP%]{margin-left:1rem}.main-flex[_ngcontent-%COMP%]{display:flex;flex-direction:column}.button-flex[_ngcontent-%COMP%]{display:flex;flex-direction:row-reverse;margin-top:5px}.tracker-flex[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:row}.tracker-flex[_ngcontent-%COMP%]   section.properties[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{display:block}.tracker-flex[_ngcontent-%COMP%]   section.properties[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:#6c757d!important}.tracker-flex[_ngcontent-%COMP%]     .progress-tracker{margin:0 auto}.details-flex[_ngcontent-%COMP%]{display:flex;flex-direction:column}.complete-button[_ngcontent-%COMP%]{margin-left:5px;margin-right:5px;align-self:center}.state-select-box[_ngcontent-%COMP%]{margin-left:5px;margin-right:5px;width:100px}.directions-text-area[_ngcontent-%COMP%]{height:100%;width:100%;padding:10px}.border[_ngcontent-%COMP%]{border:3px solid black}.details-header[_ngcontent-%COMP%]{font-size:22px;margin-top:30px}  .fa.fa-spinner.fa-pulse{align-self:center}"]}),r})(),canActivate:[v.jv,k.RQ],data:{pageTitle:"Trades Request"}},{path:"",pathMatch:"full",redirectTo:"dashboard"}]}];let Ce=(()=>{class r{}return r.\u0275fac=function(t){return new(t||r)},r.\u0275mod=e.oAB({type:r}),r.\u0275inj=e.cJS({imports:[O.Bz.forChild(_e),O.Bz]}),r})(),he=(()=>{class r{}return r.\u0275fac=function(t){return new(t||r)},r.\u0275mod=e.oAB({type:r}),r.\u0275inj=e.cJS({imports:[m.ez,Ce,Z.m,b.Xg]}),r})()}}]);